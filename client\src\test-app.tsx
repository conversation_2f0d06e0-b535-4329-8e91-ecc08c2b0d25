import React from 'react';

const TestApp = () => {
  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#000',
      color: '#fff',
      minHeight: '100vh'
    }}>
      <h1>🎉 React App Funcionando!</h1>
      <p>Se você está vendo esta mensagem, o React está carregando corretamente.</p>
      
      <div style={{ marginTop: '20px' }}>
        <h2>✅ Testes de Funcionalidade:</h2>
        <ul>
          <li>✅ React renderizando</li>
          <li>✅ JavaScript executando</li>
          <li>✅ CSS aplicado</li>
          <li>✅ Build funcionando</li>
        </ul>
      </div>

      <div style={{ marginTop: '20px' }}>
        <h2>🔗 Navegação:</h2>
        <button 
          onClick={() => window.location.href = '/blog'}
          style={{
            padding: '10px 20px',
            backgroundColor: '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          Ir para Blog
        </button>
        
        <button 
          onClick={() => window.location.href = '/'}
          style={{
            padding: '10px 20px',
            backgroundColor: '#2196F3',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Voltar para Home
        </button>
      </div>

      <div style={{ marginTop: '20px', fontSize: '12px', opacity: 0.7 }}>
        <p>Timestamp: {new Date().toLocaleString()}</p>
        <p>User Agent: {navigator.userAgent}</p>
      </div>
    </div>
  );
};

export default TestApp;
