/**
 * 🤖 COMPONENTE INVISÍVEL DE AUTOMAÇÃO - FREEENERGY
 * Executa automação em background sem interface
 */

import { useEffect } from 'react';
import useAutoBlogger from '../hooks/useAutoBlogger';

const AutoBloggerBackground = () => {
  const { 
    isActive, 
    startAutomation, 
    config,
    getStatus 
  } = useAutoBlogger();

  useEffect(() => {
    console.log('🤖 AutoBlogger Background iniciado');
    
    // Auto-inicia se configurado
    if (config.autoStart && !isActive) {
      console.log('🚀 Auto-iniciando automação...');
      setTimeout(() => {
        startAutomation();
      }, 10000); // 10 segundos após carregar
    }

    // Log status periodicamente
    const statusInterval = setInterval(() => {
      const status = getStatus();
      console.log('📊 AutoBlogger Status:', {
        ativo: status.isActive,
        gerando: status.isGenerating,
        podeGerar: status.canGenerateToday,
        totalGerado: status.totalGenerated,
        hojGerado: status.todayGenerated
      });
    }, 5 * 60 * 1000); // A cada 5 minutos

    return () => {
      clearInterval(statusInterval);
    };
  }, [config.autoStart, isActive, startAutomation, getStatus]);

  // Componente invisível
  return null;
};

export default AutoBloggerBackground;
