import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertLeadSchema, insertNewsletterSchema } from "@shared/schema";
import { ZodError } from "zod";
import { fromZodError } from "zod-validation-error";

export async function registerRoutes(app: Express): Promise<Server> {
  // Endpoint to submit lead from savings calculator
  app.post("/api/leads", async (req: Request, res: Response) => {
    try {
      const validatedData = insertLeadSchema.parse(req.body);
      const result = await storage.createLead(validatedData);
      
      // Calculate estimated savings based on consumption (30% of yearly bill)
      const consumption = parseFloat(result.consumption) || 0;
      const yearlySavings = Math.round(consumption * 12 * 0.3);
      
      res.status(201).json({ 
        success: true, 
        message: "Lead successfully created",
        data: {
          ...result,
          estimatedSavings: yearlySavings
        }
      });
    } catch (error) {
      if (error instanceof ZodError) {
        res.status(400).json({ 
          success: false, 
          message: "Validation error", 
          errors: fromZodError(error).message 
        });
      } else {
        res.status(500).json({ 
          success: false, 
          message: "Failed to create lead" 
        });
      }
    }
  });

  // Endpoint to submit newsletter subscription
  app.post("/api/newsletters", async (req: Request, res: Response) => {
    try {
      const validatedData = insertNewsletterSchema.parse(req.body);
      const result = await storage.createNewsletter(validatedData);
      
      res.status(201).json({ 
        success: true, 
        message: "Newsletter subscription successful"
      });
    } catch (error) {
      if (error instanceof ZodError) {
        res.status(400).json({ 
          success: false, 
          message: "Validation error", 
          errors: fromZodError(error).message 
        });
      } else {
        res.status(500).json({ 
          success: false, 
          message: "Failed to subscribe to newsletter" 
        });
      }
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
