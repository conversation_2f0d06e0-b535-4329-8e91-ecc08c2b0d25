import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import {
  insertLeadSchema,
  insertNewsletterSchema,
  insertBlogPostSchema,
  updateBlogPostSchema,
  blogPostFiltersSchema
} from "@shared/schema";
import { ZodError } from "zod";
import { fromZodError } from "zod-validation-error";

export async function registerRoutes(app: Express): Promise<Server> {
  // Endpoint to submit lead from savings calculator
  app.post("/api/leads", async (req: Request, res: Response) => {
    try {
      const validatedData = insertLeadSchema.parse(req.body);
      const result = await storage.createLead(validatedData);
      
      // Calculate estimated savings based on consumption (30% of yearly bill)
      const consumption = parseFloat(result.consumption) || 0;
      const yearlySavings = Math.round(consumption * 12 * 0.3);
      
      res.status(201).json({ 
        success: true, 
        message: "Lead successfully created",
        data: {
          ...result,
          estimatedSavings: yearlySavings
        }
      });
    } catch (error) {
      if (error instanceof ZodError) {
        res.status(400).json({ 
          success: false, 
          message: "Validation error", 
          errors: fromZodError(error).message 
        });
      } else {
        res.status(500).json({ 
          success: false, 
          message: "Failed to create lead" 
        });
      }
    }
  });

  // Endpoint to submit newsletter subscription
  app.post("/api/newsletters", async (req: Request, res: Response) => {
    try {
      const validatedData = insertNewsletterSchema.parse(req.body);
      const result = await storage.createNewsletter(validatedData);
      
      res.status(201).json({ 
        success: true, 
        message: "Newsletter subscription successful"
      });
    } catch (error) {
      if (error instanceof ZodError) {
        res.status(400).json({ 
          success: false, 
          message: "Validation error", 
          errors: fromZodError(error).message 
        });
      } else {
        res.status(500).json({ 
          success: false, 
          message: "Failed to subscribe to newsletter" 
        });
      }
    }
  });

  // ========================================
  // BLOG API ENDPOINTS
  // ========================================

  // GET /api/blog/posts - Listar posts com filtros
  app.get("/api/blog/posts", async (req: Request, res: Response) => {
    try {
      const filters = blogPostFiltersSchema.parse(req.query);
      const result = await storage.getAllBlogPosts(filters);

      res.json({
        success: true,
        data: result.posts,
        pagination: {
          total: result.total,
          limit: filters.limit,
          offset: filters.offset,
          hasMore: (filters.offset + filters.limit) < result.total
        }
      });
    } catch (error) {
      if (error instanceof ZodError) {
        res.status(400).json({
          success: false,
          message: "Invalid filters",
          errors: fromZodError(error).message
        });
      } else {
        res.status(500).json({
          success: false,
          message: "Failed to fetch posts"
        });
      }
    }
  });

  // GET /api/blog/posts/:id - Buscar post por ID
  app.get("/api/blog/posts/:id", async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({
          success: false,
          message: "Invalid post ID"
        });
      }

      const post = await storage.getBlogPost(id);
      if (!post) {
        return res.status(404).json({
          success: false,
          message: "Post not found"
        });
      }

      // Incrementar views
      await storage.incrementBlogPostViews(id);

      res.json({
        success: true,
        data: post
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Failed to fetch post"
      });
    }
  });

  // GET /api/blog/posts/slug/:slug - Buscar post por slug
  app.get("/api/blog/posts/slug/:slug", async (req: Request, res: Response) => {
    try {
      const slug = req.params.slug;
      const post = await storage.getBlogPostBySlug(slug);

      if (!post) {
        return res.status(404).json({
          success: false,
          message: "Post not found"
        });
      }

      // Incrementar views
      await storage.incrementBlogPostViews(post.id);

      res.json({
        success: true,
        data: post
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Failed to fetch post"
      });
    }
  });

  // POST /api/blog/posts - Criar novo post
  app.post("/api/blog/posts", async (req: Request, res: Response) => {
    try {
      const validatedData = insertBlogPostSchema.parse(req.body);
      const post = await storage.createBlogPost(validatedData);

      res.status(201).json({
        success: true,
        message: "Post created successfully",
        data: post
      });
    } catch (error) {
      if (error instanceof ZodError) {
        res.status(400).json({
          success: false,
          message: "Validation error",
          errors: fromZodError(error).message
        });
      } else {
        res.status(500).json({
          success: false,
          message: "Failed to create post"
        });
      }
    }
  });

  // PUT /api/blog/posts/:id - Atualizar post
  app.put("/api/blog/posts/:id", async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({
          success: false,
          message: "Invalid post ID"
        });
      }

      const validatedData = updateBlogPostSchema.parse({ ...req.body, id });
      const post = await storage.updateBlogPost(id, validatedData);

      res.json({
        success: true,
        message: "Post updated successfully",
        data: post
      });
    } catch (error) {
      if (error instanceof ZodError) {
        res.status(400).json({
          success: false,
          message: "Validation error",
          errors: fromZodError(error).message
        });
      } else if (error instanceof Error && error.message.includes("not found")) {
        res.status(404).json({
          success: false,
          message: "Post not found"
        });
      } else {
        res.status(500).json({
          success: false,
          message: "Failed to update post"
        });
      }
    }
  });

  // DELETE /api/blog/posts/:id - Deletar post
  app.delete("/api/blog/posts/:id", async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({
          success: false,
          message: "Invalid post ID"
        });
      }

      const deleted = await storage.deleteBlogPost(id);
      if (!deleted) {
        return res.status(404).json({
          success: false,
          message: "Post not found"
        });
      }

      res.json({
        success: true,
        message: "Post deleted successfully"
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Failed to delete post"
      });
    }
  });

  // POST /api/blog/posts/:id/like - Curtir post
  app.post("/api/blog/posts/:id/like", async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({
          success: false,
          message: "Invalid post ID"
        });
      }

      await storage.incrementBlogPostLikes(id);
      const post = await storage.getBlogPost(id);

      res.json({
        success: true,
        message: "Post liked successfully",
        data: { likes: post?.likes || 0 }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Failed to like post"
      });
    }
  });

  // POST /api/blog/posts/:id/fact-check - Atualizar fact-check
  app.post("/api/blog/posts/:id/fact-check", async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({
          success: false,
          message: "Invalid post ID"
        });
      }

      const { score, sources } = req.body;
      if (typeof score !== 'number' || score < 0 || score > 100) {
        return res.status(400).json({
          success: false,
          message: "Score must be a number between 0 and 100"
        });
      }

      await storage.updateFactCheckScore(id, score, sources);

      res.json({
        success: true,
        message: "Fact-check updated successfully"
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Failed to update fact-check"
      });
    }
  });

  // POST /api/blog/posts/:id/seo-score - Atualizar SEO score
  app.post("/api/blog/posts/:id/seo-score", async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({
          success: false,
          message: "Invalid post ID"
        });
      }

      const { score } = req.body;
      if (typeof score !== 'number' || score < 0 || score > 100) {
        return res.status(400).json({
          success: false,
          message: "Score must be a number between 0 and 100"
        });
      }

      await storage.updateSEOScore(id, score);

      res.json({
        success: true,
        message: "SEO score updated successfully"
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Failed to update SEO score"
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
