import { pgTable, text, serial, integer, boolean, timestamp, json } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const leads = pgTable("leads", {
  id: serial("id").primaryKey(),
  propertyType: text("property_type").notNull(),
  consumption: text("consumption").notNull(),
  location: text("location").notNull(),
  name: text("name").notNull(),
  contact: text("contact").notNull(),
  createdAt: text("created_at").notNull().default(new Date().toISOString()),
});

export const newsletters = pgTable("newsletters", {
  id: serial("id").primaryKey(),
  email: text("email").notNull().unique(),
  createdAt: text("created_at").notNull().default(new Date().toISOString()),
});

// Nova tabela para posts do blog
export const blogPosts = pgTable("blog_posts", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  slug: text("slug").notNull().unique(),
  content: text("content").notNull(),
  excerpt: text("excerpt").notNull(),
  metaTitle: text("meta_title"),
  metaDescription: text("meta_description"),
  keywords: json("keywords").$type<string[]>().default([]),
  tags: json("tags").$type<string[]>().default([]),
  category: text("category").notNull().default("Energia Solar"),
  author: text("author").notNull().default("FreeEnergy"),
  status: text("status").notNull().default("published"), // draft, published, archived
  featured: boolean("featured").default(false),
  viral: boolean("viral").default(false),
  readTime: integer("read_time").default(5),
  views: integer("views").default(0),
  likes: integer("likes").default(0),
  imageUrl: text("image_url"),
  publishedAt: timestamp("published_at").defaultNow(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),

  // Campos para validação anti-fake news
  factChecked: boolean("fact_checked").default(false),
  factCheckScore: integer("fact_check_score").default(0), // 0-100
  sources: json("sources").$type<string[]>().default([]),
  aiGenerated: boolean("ai_generated").default(false),
  aiModel: text("ai_model"), // gemini, openai, etc

  // SEO e Analytics
  seoScore: integer("seo_score").default(0),
  socialShares: integer("social_shares").default(0),
  avgTimeOnPage: integer("avg_time_on_page").default(0),
});

// Schemas de validação
export const insertLeadSchema = createInsertSchema(leads).omit({
  id: true,
  createdAt: true,
});

export const insertNewsletterSchema = createInsertSchema(newsletters).omit({
  id: true,
  createdAt: true,
});

export const insertBlogPostSchema = createInsertSchema(blogPosts).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  publishedAt: true,
  views: true,
  likes: true,
  socialShares: true,
  avgTimeOnPage: true,
}).extend({
  // Validações customizadas
  title: z.string().min(10).max(200),
  slug: z.string().min(5).max(100).regex(/^[a-z0-9-]+$/),
  content: z.string().min(500), // Mínimo 500 caracteres
  excerpt: z.string().min(50).max(300),
  category: z.enum(["Energia Solar", "Economia", "Tecnologia", "Sustentabilidade", "Investimento", "URGENTE", "Renda Extra", "Oportunidade"]),
  status: z.enum(["draft", "published", "archived"]).default("published"),
  readTime: z.number().min(1).max(60).default(5),
  factCheckScore: z.number().min(0).max(100).default(0),
});

export const updateBlogPostSchema = insertBlogPostSchema.partial().extend({
  id: z.number(),
});

// Schemas para busca e filtros
export const blogPostFiltersSchema = z.object({
  category: z.string().optional(),
  status: z.enum(["draft", "published", "archived"]).optional(),
  featured: z.boolean().optional(),
  viral: z.boolean().optional(),
  factChecked: z.boolean().optional(),
  aiGenerated: z.boolean().optional(),
  search: z.string().optional(),
  limit: z.number().min(1).max(100).default(10),
  offset: z.number().min(0).default(0),
  sortBy: z.enum(["publishedAt", "views", "likes", "createdAt", "title"]).default("publishedAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// Types
export type InsertLead = z.infer<typeof insertLeadSchema>;
export type Lead = typeof leads.$inferSelect;

export type InsertNewsletter = z.infer<typeof insertNewsletterSchema>;
export type Newsletter = typeof newsletters.$inferSelect;

export type InsertBlogPost = z.infer<typeof insertBlogPostSchema>;
export type UpdateBlogPost = z.infer<typeof updateBlogPostSchema>;
export type BlogPost = typeof blogPosts.$inferSelect;
export type BlogPostFilters = z.infer<typeof blogPostFiltersSchema>;
