/**
 * 🛡️ SISTEMA ANTI-FAKE NEWS - FREEENERGY
 * Valida conteúdo e previne informações falsas sobre energia solar
 */

class FactChecker {
  constructor() {
    // Fontes confiáveis para validação
    this.trustedSources = [
      'aneel.gov.br',
      'mme.gov.br',
      'gov.br',
      'absolar.org.br',
      'portal.solar',
      'greensolar.com.br',
      'neosolar.com.br',
      'canadiansolar.com',
      'jinko-solar.com',
      'trina-solar.com'
    ];

    // Palavras-chave que requerem validação extra
    this.sensitiveKeywords = [
      'governo',
      'lei',
      'regulamentação',
      'aneel',
      'tarifa',
      'imposto',
      'subsídio',
      'financiamento',
      'prazo',
      'deadline',
      'urgente',
      'último dia',
      'acaba',
      'termina'
    ];

    // Frases que são red flags para fake news
    this.redFlags = [
      'governo vai proibir',
      'última chance',
      'vai acabar amanhã',
      'segredo que eles não querem',
      'descoberta revolucionária',
      'método secreto',
      'truque simples',
      'ganhe dinheiro fácil',
      'sem esforço',
      'garantido 100%'
    ];

    // Dados verificados sobre energia solar
    this.verifiedFacts = {
      economyRange: { min: 70, max: 95 }, // % de economia real
      paybackTime: { min: 4, max: 8 }, // anos para retorno do investimento
      panelLifespan: { min: 20, max: 25 }, // anos de vida útil
      efficiency: { min: 15, max: 22 }, // % eficiência dos painéis
      priceRange: { min: 15000, max: 80000 } // faixa de preço em R$
    };
  }

  /**
   * 🔍 ANÁLISE PRINCIPAL DE FACT-CHECK
   */
  async analyzeContent(content, title = '', sources = []) {
    console.log('🛡️ Iniciando fact-check do conteúdo...');
    
    const analysis = {
      score: 100, // Começa com 100 e vai diminuindo
      issues: [],
      warnings: [],
      suggestions: [],
      sources: sources,
      factChecked: false,
      trustLevel: 'high' // high, medium, low
    };

    try {
      // 1. Verificar red flags
      this.checkRedFlags(content + ' ' + title, analysis);
      
      // 2. Verificar dados numéricos
      this.validateNumericClaims(content, analysis);
      
      // 3. Verificar fontes
      this.validateSources(sources, analysis);
      
      // 4. Verificar palavras sensíveis
      this.checkSensitiveContent(content + ' ' + title, analysis);
      
      // 5. Verificar estrutura do conteúdo
      this.validateContentStructure(content, analysis);
      
      // 6. Calcular score final
      this.calculateFinalScore(analysis);
      
      console.log(`✅ Fact-check concluído. Score: ${analysis.score}%`);
      return analysis;
      
    } catch (error) {
      console.error('❌ Erro no fact-check:', error);
      analysis.score = 50;
      analysis.issues.push('Erro na análise de fact-check');
      analysis.trustLevel = 'low';
      return analysis;
    }
  }

  /**
   * 🚨 VERIFICAR RED FLAGS
   */
  checkRedFlags(text, analysis) {
    const lowerText = text.toLowerCase();
    
    this.redFlags.forEach(flag => {
      if (lowerText.includes(flag.toLowerCase())) {
        analysis.score -= 20;
        analysis.issues.push(`Red flag detectado: "${flag}"`);
        analysis.trustLevel = 'low';
      }
    });
  }

  /**
   * 📊 VALIDAR DADOS NUMÉRICOS
   */
  validateNumericClaims(content, analysis) {
    // Verificar claims de economia
    const economyMatches = content.match(/economize?\s+(?:até\s+)?(\d+)%/gi);
    if (economyMatches) {
      economyMatches.forEach(match => {
        const percentage = parseInt(match.match(/(\d+)/)[1]);
        if (percentage > this.verifiedFacts.economyRange.max) {
          analysis.score -= 15;
          analysis.issues.push(`Claim de economia exagerado: ${percentage}% (máximo verificado: ${this.verifiedFacts.economyRange.max}%)`);
        }
      });
    }

    // Verificar claims de payback
    const paybackMatches = content.match(/(?:retorno|payback).*?(\d+)\s*(?:anos?|meses?)/gi);
    if (paybackMatches) {
      paybackMatches.forEach(match => {
        const time = parseInt(match.match(/(\d+)/)[1]);
        const unit = match.toLowerCase().includes('mes') ? 'meses' : 'anos';
        
        if (unit === 'anos' && time < this.verifiedFacts.paybackTime.min) {
          analysis.score -= 10;
          analysis.warnings.push(`Payback muito otimista: ${time} anos (mínimo real: ${this.verifiedFacts.paybackTime.min} anos)`);
        }
      });
    }

    // Verificar preços irreais
    const priceMatches = content.match(/R\$\s*(\d+(?:\.\d{3})*)/g);
    if (priceMatches) {
      priceMatches.forEach(match => {
        const price = parseInt(match.replace(/[R$.\s]/g, ''));
        if (price < 10000) {
          analysis.score -= 10;
          analysis.warnings.push(`Preço muito baixo mencionado: R$ ${price.toLocaleString()}`);
        }
      });
    }
  }

  /**
   * 📚 VALIDAR FONTES
   */
  validateSources(sources, analysis) {
    if (!sources || sources.length === 0) {
      analysis.score -= 10;
      analysis.warnings.push('Nenhuma fonte citada');
      return;
    }

    let trustedSourceCount = 0;
    sources.forEach(source => {
      const isTrusted = this.trustedSources.some(trusted => 
        source.toLowerCase().includes(trusted)
      );
      
      if (isTrusted) {
        trustedSourceCount++;
      }
    });

    if (trustedSourceCount === 0) {
      analysis.score -= 15;
      analysis.issues.push('Nenhuma fonte confiável citada');
    } else if (trustedSourceCount < sources.length / 2) {
      analysis.score -= 5;
      analysis.warnings.push('Poucas fontes confiáveis');
    }

    analysis.suggestions.push(`Adicionar mais fontes confiáveis: ${this.trustedSources.slice(0, 3).join(', ')}`);
  }

  /**
   * ⚠️ VERIFICAR CONTEÚDO SENSÍVEL
   */
  checkSensitiveContent(text, analysis) {
    const lowerText = text.toLowerCase();
    let sensitiveCount = 0;

    this.sensitiveKeywords.forEach(keyword => {
      if (lowerText.includes(keyword)) {
        sensitiveCount++;
      }
    });

    if (sensitiveCount > 3) {
      analysis.score -= 5;
      analysis.warnings.push('Conteúdo com muitas informações sensíveis - verificar precisão');
    }
  }

  /**
   * 📝 VALIDAR ESTRUTURA DO CONTEÚDO
   */
  validateContentStructure(content, analysis) {
    // Verificar se tem conteúdo suficiente
    if (content.length < 500) {
      analysis.score -= 10;
      analysis.warnings.push('Conteúdo muito curto para análise completa');
    }

    // Verificar se tem títulos estruturados
    const hasHeaders = /<h[1-6]>/i.test(content);
    if (!hasHeaders) {
      analysis.score -= 5;
      analysis.suggestions.push('Adicionar títulos estruturados (H1, H2, H3)');
    }

    // Verificar se tem parágrafos bem estruturados
    const paragraphCount = (content.match(/<p>/g) || []).length;
    if (paragraphCount < 3) {
      analysis.score -= 5;
      analysis.suggestions.push('Estruturar melhor o conteúdo em parágrafos');
    }
  }

  /**
   * 🎯 CALCULAR SCORE FINAL
   */
  calculateFinalScore(analysis) {
    // Garantir que o score não seja negativo
    analysis.score = Math.max(0, Math.min(100, analysis.score));
    
    // Definir nível de confiança
    if (analysis.score >= 80) {
      analysis.trustLevel = 'high';
      analysis.factChecked = true;
    } else if (analysis.score >= 60) {
      analysis.trustLevel = 'medium';
      analysis.factChecked = true;
    } else {
      analysis.trustLevel = 'low';
      analysis.factChecked = false;
    }

    // Adicionar recomendações baseadas no score
    if (analysis.score < 70) {
      analysis.suggestions.push('Revisar informações com fontes oficiais');
      analysis.suggestions.push('Adicionar disclaimers apropriados');
    }
  }

  /**
   * 🔧 SUGERIR MELHORIAS
   */
  suggestImprovements(analysis) {
    const improvements = [];

    if (analysis.score < 80) {
      improvements.push('Adicionar mais fontes confiáveis');
      improvements.push('Verificar dados numéricos com fontes oficiais');
    }

    if (analysis.issues.length > 0) {
      improvements.push('Corrigir problemas identificados');
    }

    if (analysis.warnings.length > 0) {
      improvements.push('Revisar pontos de atenção');
    }

    return improvements;
  }

  /**
   * 📋 GERAR RELATÓRIO COMPLETO
   */
  generateReport(analysis) {
    return {
      score: analysis.score,
      trustLevel: analysis.trustLevel,
      factChecked: analysis.factChecked,
      summary: `Análise concluída com score ${analysis.score}% e nível de confiança ${analysis.trustLevel}`,
      issues: analysis.issues,
      warnings: analysis.warnings,
      suggestions: analysis.suggestions,
      improvements: this.suggestImprovements(analysis),
      timestamp: new Date().toISOString()
    };
  }
}

// Exportar instância singleton
const factChecker = new FactChecker();
export default factChecker;
