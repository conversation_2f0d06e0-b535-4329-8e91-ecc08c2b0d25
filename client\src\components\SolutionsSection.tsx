import { motion } from 'framer-motion';
import { ArrowRight, SunIcon, PlugZap, Home, Building2, Factory, Landmark } from 'lucide-react';

const SolutionsSection = () => {
  const solutions = [
    {
      icon: <SunIcon className="h-8 w-8 text-white" />,
      title: "Energia Solar",
      description: "Reduza sua conta de luz em até 95% com soluções solares sem investimento inicial.",
      color: "#FFC107",
      tags: ["Residencial", "Comercial", "Industrial"],
      link: "#simulador"
    },
    {
      icon: <PlugZap className="h-8 w-8 text-white" />,
      title: "Mercado Livre de Energia",
      description: "Acesse energia mais barata através do mercado livre, com economia garantida de 15% a 30%.",
      color: "#2ECC71",
      tags: ["Comercial", "Industrial"],
      link: "https://www.leadenergy.com.br/simular?ch=0f0547d3-7e73-442e-a915-34ef39efe74b&pr=a048c6c3-6224-43ab-ad95-7727cf36d2a2"
    },
    {
      icon: <Home className="h-8 w-8 text-white" />,
      title: "Automação Residencial",
      description: "Tecnologia inteligente para controle e redução do consumo de energia em sua casa.",
      color: "#3498DB",
      tags: ["Residencial"],
      link: "#simulador"
    },
    {
      icon: <Building2 className="h-8 w-8 text-white" />,
      title: "Gestão Energética",
      description: "Monitore e otimize o consumo de energia do seu negócio com nossa plataforma inteligente.",
      color: "#9B59B6",
      tags: ["Comercial", "Industrial"],
      link: "#simulador"
    }
  ];

  const clientTypes = [
    {
      icon: <Home className="h-8 w-8 text-white" />,
      title: "Residencial",
      description: "Para casas e apartamentos",
      color: "#2ECC71"
    },
    {
      icon: <Building2 className="h-8 w-8 text-white" />,
      title: "Comercial",
      description: "Para pequenos e médios negócios",
      color: "#FFC107"
    },
    {
      icon: <Factory className="h-8 w-8 text-white" />,
      title: "Industrial",
      description: "Para fábricas e grandes operações",
      color: "#3498DB"
    },
    {
      icon: <Landmark className="h-8 w-8 text-white" />,
      title: "Institucional",
      description: "Para órgãos públicos e instituições",
      color: "#9B59B6"
    }
  ];

  return (
    <section id="solucoes" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <motion.div 
          className="max-w-2xl mx-auto text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl font-bold font-montserrat mb-4">
            Nossas <span className="text-[#2ECC71]">Soluções</span>
          </h2>
          <p className="text-lg text-gray-600 mb-6">
            Conectamos você com as melhores soluções energéticas para reduzir seus custos e aumentar a eficiência.
          </p>
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            {clientTypes.map((type, index) => (
              <motion.div 
                key={index}
                className="flex items-center gap-2 px-4 py-2 rounded-full border"
                style={{ borderColor: type.color }}
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <div 
                  className="w-8 h-8 rounded-full flex items-center justify-center"
                  style={{ backgroundColor: type.color }}
                >
                  {type.icon}
                </div>
                <span className="font-medium">{type.title}</span>
              </motion.div>
            ))}
          </div>
        </motion.div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {solutions.map((solution, index) => (
            <motion.div 
              key={index}
              className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="p-6 flex flex-col h-full">
                <div className="flex items-start mb-4">
                  <div 
                    className="w-16 h-16 rounded-xl flex items-center justify-center mr-4"
                    style={{ backgroundColor: solution.color }}
                  >
                    {solution.icon}
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">{solution.title}</h3>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {solution.tags.map((tag, tagIndex) => (
                        <span 
                          key={tagIndex} 
                          className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-600"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
                <p className="text-gray-600 mb-6">{solution.description}</p>
                <div className="mt-auto">
                  <a
                    href={solution.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 text-gray-700 font-medium hover:text-[#2ECC71] transition-colors"
                  >
                    Saiba mais <ArrowRight className="h-4 w-4" />
                  </a>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
        
        <motion.div 
          className="text-center mt-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <a 
            href="https://www.xforcepromotora.com.br/assinatura-energia?affilliate_id=YOCNIH"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 bg-[#FFC107] hover:bg-orange-500 text-white font-bold py-3 px-6 rounded-lg shadow-md transform hover:scale-105 transition-all duration-300"
          >
            Ver todas as soluções <ArrowRight className="h-5 w-5" />
          </a>
        </motion.div>
      </div>
    </section>
  );
};

export default SolutionsSection;