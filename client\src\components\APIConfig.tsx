/**
 * ⚙️ CONFIGURAÇÃO DE API KEYS - AUTOBLOGGER FREEENERGY
 * Interface para configurar chaves de API do Gemini e OpenAI
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Key, Eye, EyeOff, CheckCircle, AlertCircle, ExternalLink } from 'lucide-react';

const APIConfig = () => {
  const [config, setConfig] = useState({
    geminiKey: '',
    openaiKey: '',
    preferredAPI: 'gemini'
  });
  const [showKeys, setShowKeys] = useState({
    gemini: false,
    openai: false
  });
  const [testResults, setTestResults] = useState({
    gemini: null,
    openai: null
  });
  const [testing, setTesting] = useState({
    gemini: false,
    openai: false
  });

  // Carrega configuração salva
  useEffect(() => {
    const savedConfig = localStorage.getItem('autoblogger_api_config');
    if (savedConfig) {
      try {
        const parsed = JSON.parse(savedConfig);
        setConfig(parsed);
      } catch (error) {
        console.error('Erro ao carregar configuração de API:', error);
      }
    }
  }, []);

  // Salva configuração
  const saveConfig = () => {
    localStorage.setItem('autoblogger_api_config', JSON.stringify(config));
    
    // Também salva nas variáveis de ambiente simuladas
    if (typeof window !== 'undefined') {
      window.REACT_APP_GEMINI_API_KEY = config.geminiKey;
      window.REACT_APP_OPENAI_API_KEY = config.openaiKey;
    }
    
    alert('✅ Configuração salva com sucesso!');
  };

  // Testa API key
  const testAPIKey = async (apiType) => {
    setTesting(prev => ({ ...prev, [apiType]: true }));
    
    try {
      let response;
      
      if (apiType === 'gemini') {
        const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${config.geminiKey}`;
        response = await fetch(url, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            contents: [{
              parts: [{ text: 'Teste de conexão. Responda apenas: OK' }]
            }]
          })
        });
      } else {
        response = await fetch('https://api.openai.com/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${config.openaiKey}`
          },
          body: JSON.stringify({
            model: 'gpt-3.5-turbo',
            messages: [{ role: 'user', content: 'Teste' }],
            max_tokens: 5
          })
        });
      }

      if (response.ok) {
        setTestResults(prev => ({ ...prev, [apiType]: 'success' }));
      } else {
        setTestResults(prev => ({ ...prev, [apiType]: 'error' }));
      }
    } catch (error) {
      setTestResults(prev => ({ ...prev, [apiType]: 'error' }));
    } finally {
      setTesting(prev => ({ ...prev, [apiType]: false }));
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <motion.div
        className="bg-white rounded-2xl shadow-lg p-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center gap-3 mb-6">
          <Key className="w-8 h-8 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-800">
            Configuração de API Keys
          </h2>
        </div>

        <div className="space-y-8">
          {/* Google Gemini API */}
          <div className="border border-gray-200 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-800">
                  🤖 Google Gemini API
                </h3>
                <p className="text-sm text-gray-600">
                  API recomendada para geração de conteúdo (Gratuita)
                </p>
              </div>
              <a
                href="https://makersuite.google.com/app/apikey"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-blue-600 hover:text-blue-700"
              >
                <ExternalLink className="w-4 h-4" />
                Obter Chave
              </a>
            </div>

            <div className="space-y-4">
              <div className="relative">
                <input
                  type={showKeys.gemini ? 'text' : 'password'}
                  placeholder="Cole sua chave da Gemini API aqui..."
                  value={config.geminiKey}
                  onChange={(e) => setConfig(prev => ({ ...prev, geminiKey: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg pr-20 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex gap-2">
                  <button
                    onClick={() => setShowKeys(prev => ({ ...prev, gemini: !prev.gemini }))}
                    className="p-1 text-gray-500 hover:text-gray-700"
                  >
                    {showKeys.gemini ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                  {testResults.gemini === 'success' && <CheckCircle className="w-5 h-5 text-green-500" />}
                  {testResults.gemini === 'error' && <AlertCircle className="w-5 h-5 text-red-500" />}
                </div>
              </div>

              <button
                onClick={() => testAPIKey('gemini')}
                disabled={!config.geminiKey || testing.gemini}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400"
              >
                {testing.gemini ? 'Testando...' : 'Testar Conexão'}
              </button>
            </div>
          </div>

          {/* OpenAI API */}
          <div className="border border-gray-200 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-800">
                  🧠 OpenAI API
                </h3>
                <p className="text-sm text-gray-600">
                  API alternativa (Fallback) - Requer créditos
                </p>
              </div>
              <a
                href="https://platform.openai.com/api-keys"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-blue-600 hover:text-blue-700"
              >
                <ExternalLink className="w-4 h-4" />
                Obter Chave
              </a>
            </div>

            <div className="space-y-4">
              <div className="relative">
                <input
                  type={showKeys.openai ? 'text' : 'password'}
                  placeholder="Cole sua chave da OpenAI API aqui..."
                  value={config.openaiKey}
                  onChange={(e) => setConfig(prev => ({ ...prev, openaiKey: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg pr-20 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex gap-2">
                  <button
                    onClick={() => setShowKeys(prev => ({ ...prev, openai: !prev.openai }))}
                    className="p-1 text-gray-500 hover:text-gray-700"
                  >
                    {showKeys.openai ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                  {testResults.openai === 'success' && <CheckCircle className="w-5 h-5 text-green-500" />}
                  {testResults.openai === 'error' && <AlertCircle className="w-5 h-5 text-red-500" />}
                </div>
              </div>

              <button
                onClick={() => testAPIKey('openai')}
                disabled={!config.openaiKey || testing.openai}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400"
              >
                {testing.openai ? 'Testando...' : 'Testar Conexão'}
              </button>
            </div>
          </div>

          {/* API Preferida */}
          <div className="border border-gray-200 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              🎯 API Preferida
            </h3>
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="preferredAPI"
                  value="gemini"
                  checked={config.preferredAPI === 'gemini'}
                  onChange={(e) => setConfig(prev => ({ ...prev, preferredAPI: e.target.value }))}
                  className="mr-3"
                />
                <span>Google Gemini (Recomendado - Gratuito)</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="preferredAPI"
                  value="openai"
                  checked={config.preferredAPI === 'openai'}
                  onChange={(e) => setConfig(prev => ({ ...prev, preferredAPI: e.target.value }))}
                  className="mr-3"
                />
                <span>OpenAI (Pago)</span>
              </label>
            </div>
          </div>

          {/* Botão Salvar */}
          <div className="flex justify-center">
            <button
              onClick={saveConfig}
              className="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-semibold"
            >
              💾 Salvar Configuração
            </button>
          </div>
        </div>

        {/* Instruções */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-blue-800 mb-2">📋 Instruções:</h4>
          <ol className="text-sm text-blue-700 space-y-1">
            <li>1. Obtenha uma chave da Google Gemini API (gratuita e recomendada)</li>
            <li>2. Opcionalmente, configure uma chave da OpenAI como backup</li>
            <li>3. Teste as conexões para verificar se estão funcionando</li>
            <li>4. Salve a configuração</li>
            <li>5. O AutoBlogger usará automaticamente as APIs configuradas</li>
          </ol>
        </div>
      </motion.div>
    </div>
  );
};

export default APIConfig;
