import { motion } from 'framer-motion';
import { Mail, Phone, MapPin, Send, Clock, ArrowRight } from 'lucide-react';

const ContactSection = () => {
  const contactInfo = [
    {
      icon: <Mail className="h-6 w-6 text-[#2ECC71]" />,
      title: "Email",
      info: "<EMAIL>",
      link: "mailto:<EMAIL>"
    },
    {
      icon: <Phone className="h-6 w-6 text-[#2ECC71]" />,
      title: "WhatsApp",
      info: "+55 (98) 98173-5618",
      link: "https://wa.me/5598981735618?text=Olá! Gostaria de saber mais sobre as soluções de energia da Free Energy."
    },
    {
      icon: <MapPin className="h-6 w-6 text-[#2ECC71]" />,
      title: "Localização",
      info: "São Luís - MA, Brasil",
      link: null
    },
    {
      icon: <Clock className="h-6 w-6 text-[#2ECC71]" />,
      title: "<PERSON><PERSON><PERSON><PERSON> Atendimento",
      info: "Segunda a Sexta: 8h às 18h",
      link: null
    }
  ];

  return (
    <section id="contact" className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-4xl font-bold font-montserrat mb-6">
            Entre em <span className="text-[#2ECC71]">Contato</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Pronto para economizar na sua conta de energia? Nossa equipe de especialistas 
            está aqui para encontrar a melhor solução para o seu perfil de consumo.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Info */}
          <motion.div
            className="space-y-8"
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div>
              <h3 className="text-2xl font-bold font-montserrat mb-6 text-gray-800">
                Fale Conosco
              </h3>
              <p className="text-gray-600 mb-8 leading-relaxed">
                Estamos prontos para ajudar você a encontrar a solução de energia 
                mais econômica e sustentável. Entre em contato através dos canais abaixo:
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {contactInfo.map((item, index) => (
                <motion.div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                  whileHover={{ y: -5 }}
                >
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 p-3 bg-[#2ECC71]/10 rounded-lg">
                      {item.icon}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-800 mb-2">{item.title}</h4>
                      {item.link ? (
                        <a
                          href={item.link}
                          target={item.link.startsWith('http') ? '_blank' : undefined}
                          rel={item.link.startsWith('http') ? 'noopener noreferrer' : undefined}
                          className="text-[#2ECC71] hover:text-green-600 transition-colors font-medium"
                        >
                          {item.info}
                        </a>
                      ) : (
                        <p className="text-gray-600">{item.info}</p>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Social Media */}
            <motion.div
              className="bg-white p-6 rounded-xl shadow-lg border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <h4 className="font-semibold text-gray-800 mb-4">Siga-nos nas Redes Sociais</h4>
              <div className="flex space-x-4">
                <a
                  href="https://www.instagram.com/freeenergybr/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:shadow-lg transition-all duration-300 transform hover:scale-110"
                >
                  <Send className="h-5 w-5" />
                </a>
                <a
                  href="https://www.facebook.com/people/Free-Energy-Economize-Com-Energia-Limpa/61575075471555/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-lg hover:shadow-lg transition-all duration-300 transform hover:scale-110"
                >
                  <Send className="h-5 w-5" />
                </a>
              </div>
            </motion.div>
          </motion.div>

          {/* CTA Section */}
          <motion.div
            className="bg-gradient-to-br from-[#2ECC71] to-green-600 p-8 rounded-2xl text-white"
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <div className="text-center">
              <h3 className="text-3xl font-bold font-montserrat mb-6">
                Comece a Economizar Hoje!
              </h3>
              <p className="text-green-100 mb-8 text-lg leading-relaxed">
                Não perca mais tempo pagando caro na conta de energia. 
                Nossa equipe está pronta para encontrar a solução ideal para você.
              </p>

              <div className="space-y-4">
                <motion.a
                  href="https://wa.me/5598981735618?text=Olá! Gostaria de saber mais sobre as soluções de energia da Free Energy."
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-3 bg-white text-[#2ECC71] font-bold py-4 px-8 rounded-xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Phone className="h-5 w-5" />
                  Falar no WhatsApp
                  <ArrowRight className="h-5 w-5" />
                </motion.a>

                <p className="text-green-100 text-sm">
                  Ou envie um email para: 
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-white font-semibold hover:underline ml-1"
                  >
                    <EMAIL>
                  </a>
                </p>
              </div>

              <div className="mt-8 p-6 bg-white/10 rounded-xl backdrop-blur-sm">
                <h4 className="font-semibold mb-3">Por que escolher a Free Energy?</h4>
                <ul className="text-green-100 text-sm space-y-2 text-left">
                  <li>✅ Análise gratuita do seu perfil de consumo</li>
                  <li>✅ Conexão com os melhores parceiros do mercado</li>
                  <li>✅ Economia de até 40% na conta de energia</li>
                  <li>✅ Suporte especializado durante todo o processo</li>
                </ul>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
