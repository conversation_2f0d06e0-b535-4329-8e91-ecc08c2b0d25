import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, ArrowLeft, Brain, Sparkles, Atom } from 'lucide-react';
import { useLocation } from 'wouter';
import { useEffect, useState } from 'react';

const EnercyLanding = () => {
  const [, setLocation] = useLocation();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const handleBackToHome = () => {
    setLocation('/');
  };

  console.log('🚀 EnercyLanding component loaded! URL: /enercy');
  return (
    <div className="min-h-screen bg-black text-white overflow-hidden relative">
      {/* Dynamic Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-green-900"></div>

        {/* AI Particles */}
        <div className="absolute inset-0 opacity-20">
          {Array.from({ length: 30 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-green-400 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                scale: [0, 1, 0],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>

        {/* Mouse Follower */}
        <motion.div
          className="absolute w-96 h-96 bg-green-500/10 rounded-full pointer-events-none blur-3xl"
          animate={{
            x: mousePosition.x - 192,
            y: mousePosition.y - 192,
          }}
          transition={{ type: "spring", damping: 30, stiffness: 200 }}
        />
      </div>

      {/* Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-black/10 backdrop-blur-xl border-b border-green-400/20">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <motion.button
            onClick={handleBackToHome}
            className="flex items-center gap-2 text-green-400 hover:text-green-300 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <ArrowLeft className="h-5 w-5" />
            <span className="font-semibold">Voltar para FreeEnergy</span>
          </motion.button>

          <motion.div
            className="text-3xl font-black"
            animate={{
              textShadow: [
                "0 0 10px rgba(34, 197, 94, 0.5)",
                "0 0 20px rgba(34, 197, 94, 0.8)",
                "0 0 10px rgba(34, 197, 94, 0.5)"
              ]
            }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <span className="bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
              ENERCY
            </span>
          </motion.div>

          <motion.button
            className="px-6 py-3 bg-gradient-to-r from-green-500 to-blue-600 rounded-full font-bold text-sm hover:shadow-lg hover:shadow-green-500/50 transition-all duration-300 border border-green-400/30"
            whileHover={{ scale: 1.05, boxShadow: "0 0 30px rgba(34, 197, 94, 0.6)" }}
            whileTap={{ scale: 0.95 }}
          >
            <Sparkles className="inline mr-2 h-4 w-4" />
            Acesso Antecipado
          </motion.button>
        </div>
      </header>
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center pt-20 z-10">
        <motion.div
          className="text-center z-20 max-w-6xl mx-auto px-4 relative"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
        >
          <motion.h1
            className="text-8xl md:text-9xl lg:text-[12rem] font-black mb-6 bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 bg-clip-text text-transparent relative z-10"
            initial={{ scale: 0.5, rotateY: -180 }}
            animate={{ scale: 1, rotateY: 0 }}
            transition={{ duration: 1.5, type: "spring", bounce: 0.4 }}
          >
            ENERCY
          </motion.h1>

          <motion.div
            className="text-2xl md:text-4xl mb-8 font-light relative"
            initial={{ opacity: 0, x: -100 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5, duration: 1, type: "spring" }}
          >
            <Brain className="inline mr-3 h-8 w-8 text-green-400" />
            <span className="text-green-400">O Sistema Operacional</span> da Nova Energia
            <Atom className="inline ml-3 h-8 w-8 text-blue-400 animate-spin" style={{ animationDuration: '3s' }} />
          </motion.div>

          <motion.p
            className="text-lg md:text-2xl mb-12 text-gray-300 max-w-4xl mx-auto leading-relaxed font-light"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1, duration: 1 }}
          >
            Infraestrutura digital que <span className="text-green-400 font-semibold">tokeniza</span>,
            <span className="text-blue-400 font-semibold"> regula</span> e
            <span className="text-purple-400 font-semibold"> democratiza</span> energia limpa via blockchain.
            <br />
            Como <span className="text-blue-400 font-bold">Ethereum para finanças</span>,
            <span className="text-green-400 font-bold"> ENERCY é para energia</span>.
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row gap-6 justify-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.5, duration: 0.8 }}
          >
            <motion.button
              className="px-8 py-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-full font-bold text-lg shadow-2xl hover:shadow-green-500/25 transition-all duration-300"
              whileHover={{ scale: 1.05, boxShadow: "0 0 30px rgba(34, 197, 94, 0.5)" }}
              whileTap={{ scale: 0.95 }}
            >
              Entrar no Futuro <ArrowRight className="inline ml-2" />
            </motion.button>

            <motion.button
              onClick={handleBackToHome}
              className="px-8 py-4 border-2 border-green-400 rounded-full font-bold text-lg hover:bg-green-400 hover:text-black transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Voltar para Free Energy
            </motion.button>
          </motion.div>
        </motion.div>
      </section>
    </div>
  );
};

export default EnercyLanding;
