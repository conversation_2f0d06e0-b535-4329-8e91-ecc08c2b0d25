import { motion, useScroll, useTransform } from 'framer-motion';
import { Network, Coins, Zap, Globe, Database, Cpu, TrendingUp, Shield, CheckCircle, ArrowRight, Brain, Sparkles, Atom, Activity, BarChart3, Orbit, Layers } from 'lucide-react';
import { useState, useEffect, useRef } from 'react';
import { useLocation } from 'wouter';

export const BlockchainVisualizer = () => {
  return (
    <section className="py-20 bg-gradient-to-b from-gray-900 to-black">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-5xl font-bold mb-6 bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent">
            Blockchain Energética
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Cada kWh tokenizado, rastreável e negociável em tempo real
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {[
            {
              icon: <Network className="h-12 w-12" />,
              title: "Conexão Global",
              description: "Rede descentralizada conectando geradores e consumidores mundialmente",
              color: "from-green-400 to-emerald-600"
            },
            {
              icon: <Coins className="h-12 w-12" />,
              title: "Tokenização ERNC",
              description: "Cada unidade de energia convertida em tokens rastreáveis na blockchain",
              color: "from-blue-400 to-cyan-600"
            },
            {
              icon: <Shield className="h-12 w-12" />,
              title: "Segurança Total",
              description: "Smart contracts garantem transparência e execução automática",
              color: "from-purple-400 to-pink-600"
            }
          ].map((item, index) => (
            <motion.div
              key={index}
              className="relative group"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
            >
              <div className="absolute inset-0 bg-gradient-to-r opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-xl" 
                   style={{ background: `linear-gradient(135deg, ${item.color.split(' ')[1]}, ${item.color.split(' ')[3]})` }}></div>
              
              <div className="relative bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-2xl p-8 hover:border-green-400/50 transition-all duration-300">
                <div className={`inline-flex p-4 rounded-xl bg-gradient-to-r ${item.color} mb-6`}>
                  {item.icon}
                </div>
                <h3 className="text-2xl font-bold mb-4 text-white">{item.title}</h3>
                <p className="text-gray-300 leading-relaxed">{item.description}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export const StakingInterface = () => {
  const [stakingAmount, setStakingAmount] = useState(1000);
  const [stakingPeriod, setStakingPeriod] = useState(12);

  const calculateRewards = () => {
    const apy = 0.125; // 12.5%
    const monthlyReturn = (stakingAmount * apy * stakingPeriod) / 12;
    return monthlyReturn;
  };

  return (
    <section className="py-20 bg-gradient-to-b from-black to-gray-900">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-5xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
            Staking Energético
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Invista em energia limpa e ganhe recompensas por financiar o futuro sustentável
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          <motion.div
            className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-lg border border-gray-600 rounded-3xl p-8 shadow-2xl"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-3xl font-bold mb-6 text-green-400">Dashboard de Staking</h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-4 bg-gray-700/50 rounded-xl">
                    <span className="text-gray-300">APY Atual</span>
                    <span className="text-2xl font-bold text-green-400">12.5%</span>
                  </div>
                  <div className="flex justify-between items-center p-4 bg-gray-700/50 rounded-xl">
                    <span className="text-gray-300">Tokens em Staking</span>
                    <span className="text-2xl font-bold text-blue-400">1,250 ERNC</span>
                  </div>
                  <div className="flex justify-between items-center p-4 bg-gray-700/50 rounded-xl">
                    <span className="text-gray-300">Recompensas</span>
                    <span className="text-2xl font-bold text-yellow-400">R$ {calculateRewards().toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</span>
                  </div>
                </div>

                {/* Simulador de Staking */}
                <div className="mt-6 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Valor para Staking: R$ {stakingAmount.toLocaleString('pt-BR')}
                    </label>
                    <input
                      type="range"
                      min="1000"
                      max="100000"
                      step="1000"
                      value={stakingAmount}
                      onChange={(e) => setStakingAmount(Number(e.target.value))}
                      className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Período: {stakingPeriod} meses
                    </label>
                    <input
                      type="range"
                      min="3"
                      max="36"
                      step="3"
                      value={stakingPeriod}
                      onChange={(e) => setStakingPeriod(Number(e.target.value))}
                      className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                    />
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-3xl font-bold mb-6 text-blue-400">Como Funciona</h3>
                <div className="space-y-4">
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-black font-bold">1</div>
                    <div>
                      <h4 className="font-bold text-white">Compre Tokens ERNC</h4>
                      <p className="text-gray-400 text-sm">Adquira tokens ERNC na plataforma</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">2</div>
                    <div>
                      <h4 className="font-bold text-white">Faça Staking</h4>
                      <p className="text-gray-400 text-sm">Bloqueie seus tokens para financiar operações</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center text-black font-bold">3</div>
                    <div>
                      <h4 className="font-bold text-white">Ganhe Recompensas</h4>
                      <p className="text-gray-400 text-sm">Receba parte das transações energéticas</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export const EarlyAccess = () => {
  const [, setLocation] = useLocation();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    whatsapp: '',
    investorType: '',
    investmentAmount: ''
  });
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simular envio do formulário
    await new Promise(resolve => setTimeout(resolve, 2000));

    setIsSubmitted(true);
    setIsLoading(false);

    // Opcional: Enviar dados para um endpoint
    console.log('Dados do formulário:', formData);
  };

  const handleBackToFreeEnergy = () => {
    setLocation('/#o-futuro');
  };

  if (isSubmitted) {
    return (
      <section className="py-20 bg-gradient-to-b from-gray-900 to-black">
        <div className="container mx-auto px-4">
          <motion.div
            className="max-w-2xl mx-auto text-center"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <motion.div
              className="bg-gradient-to-br from-green-500/20 to-blue-500/20 backdrop-blur-lg border border-green-400/30 rounded-3xl p-12 shadow-2xl"
              initial={{ y: 20 }}
              animate={{ y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <CheckCircle className="h-16 w-16 text-green-400 mx-auto mb-6" />
              <h2 className="text-4xl font-bold mb-4 text-green-400">Cadastro Realizado!</h2>
              <p className="text-xl text-gray-300 mb-8">
                Parabéns! Você está na lista de acesso antecipado ao ENERCY.
                Em breve entraremos em contato com mais informações sobre o futuro da energia.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <motion.button
                  onClick={handleBackToFreeEnergy}
                  className="px-6 py-3 bg-gradient-to-r from-green-500 to-blue-600 rounded-full font-bold hover:shadow-lg hover:shadow-green-500/25 transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Explorar FreeEnergy <ArrowRight className="inline ml-2 h-4 w-4" />
                </motion.button>
                <motion.button
                  onClick={() => setIsSubmitted(false)}
                  className="px-6 py-3 border-2 border-green-400 rounded-full font-bold hover:bg-green-400 hover:text-black transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Novo Cadastro
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-gradient-to-b from-gray-900 to-black">
      <div className="container mx-auto px-4">
        <motion.div
          className="max-w-4xl mx-auto text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-5xl font-bold mb-6 bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
            Acesso Antecipado
          </h2>
          <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
            Seja um dos primeiros a participar da revolução energética. 
            Cadastre-se para acesso exclusivo ao token ERNC.
          </p>

          <form onSubmit={handleSubmit}>
            <motion.div
              className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-lg border border-gray-600 rounded-3xl p-8 shadow-2xl"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.3 }}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Nome completo"
                    required
                    className="w-full p-4 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:border-green-400 focus:outline-none transition-colors"
                  />
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Email"
                    required
                    className="w-full p-4 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:border-green-400 focus:outline-none transition-colors"
                  />
                  <input
                    type="tel"
                    name="whatsapp"
                    value={formData.whatsapp}
                    onChange={handleInputChange}
                    placeholder="WhatsApp"
                    required
                    className="w-full p-4 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:border-green-400 focus:outline-none transition-colors"
                  />
                </div>

                <div className="space-y-6">
                  <select
                    name="investorType"
                    value={formData.investorType}
                    onChange={handleInputChange}
                    required
                    className="w-full p-4 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:border-green-400 focus:outline-none transition-colors"
                  >
                    <option value="">Tipo de investidor</option>
                    <option value="pessoa-fisica">Pessoa Física</option>
                    <option value="pessoa-juridica">Pessoa Jurídica</option>
                    <option value="investidor-institucional">Investidor Institucional</option>
                  </select>
                  <input
                    type="number"
                    name="investmentAmount"
                    value={formData.investmentAmount}
                    onChange={handleInputChange}
                    placeholder="Valor de interesse (R$)"
                    min="1000"
                    required
                    className="w-full p-4 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:border-green-400 focus:outline-none transition-colors"
                  />
                  <motion.button
                    type="submit"
                    disabled={isLoading}
                    className="w-full p-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-xl font-bold text-lg shadow-2xl hover:shadow-green-500/25 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    whileHover={!isLoading ? { scale: 1.05 } : {}}
                    whileTap={!isLoading ? { scale: 0.95 } : {}}
                  >
                    {isLoading ? 'Processando...' : 'Garantir Acesso Antecipado'}
                  </motion.button>
                </div>
              </div>
            </motion.div>
          </form>
        </motion.div>
      </div>
    </section>
  );
};

// Novo componente: AI Energy Predictor
export const AIEnergyPredictor = () => {
  const [prediction, setPrediction] = useState(0);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setIsAnalyzing(true);
      setTimeout(() => {
        setPrediction(Math.random() * 100);
        setIsAnalyzing(false);
      }, 1500);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <section className="py-20 bg-gradient-to-b from-black to-gray-900 relative overflow-hidden">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-5xl font-bold mb-6 bg-gradient-to-r from-purple-400 to-pink-500 bg-clip-text text-transparent">
            <Brain className="inline mr-4 h-12 w-12 text-purple-400" />
            IA Preditiva Energética
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Algoritmos de machine learning preveem demanda, otimizam distribuição e maximizam eficiência
          </p>
        </motion.div>

        <div className="max-w-6xl mx-auto">
          <motion.div
            className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-lg border border-purple-400/30 rounded-3xl p-8 shadow-2xl relative overflow-hidden"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            {/* AI Processing Animation */}
            <div className="absolute top-4 right-4">
              <motion.div
                className={`w-4 h-4 rounded-full ${isAnalyzing ? 'bg-purple-400' : 'bg-green-400'}`}
                animate={isAnalyzing ? { scale: [1, 1.2, 1], opacity: [1, 0.5, 1] } : {}}
                transition={{ duration: 0.5, repeat: isAnalyzing ? Infinity : 0 }}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="space-y-6">
                <h3 className="text-2xl font-bold text-purple-400 mb-4">
                  <Activity className="inline mr-2 h-6 w-6" />
                  Análise em Tempo Real
                </h3>
                <div className="space-y-4">
                  <div className="bg-gray-700/50 p-4 rounded-xl">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-300">Demanda Prevista</span>
                      <span className="text-purple-400 font-bold">{prediction.toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-gray-600 rounded-full h-2">
                      <motion.div
                        className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${prediction}%` }}
                        transition={{ duration: 1 }}
                      />
                    </div>
                  </div>
                  <div className="bg-gray-700/50 p-4 rounded-xl">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300">Eficiência IA</span>
                      <span className="text-green-400 font-bold">97.3%</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <h3 className="text-2xl font-bold text-blue-400 mb-4">
                  <BarChart3 className="inline mr-2 h-6 w-6" />
                  Otimização Automática
                </h3>
                <div className="space-y-4">
                  {['Distribuição', 'Armazenamento', 'Preços'].map((item, index) => (
                    <motion.div
                      key={item}
                      className="bg-gray-700/50 p-4 rounded-xl"
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ delay: index * 0.2 }}
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300">{item}</span>
                        <motion.div
                          className="w-3 h-3 bg-green-400 rounded-full"
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Infinity, delay: index * 0.5 }}
                        />
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              <div className="space-y-6">
                <h3 className="text-2xl font-bold text-green-400 mb-4">
                  <Sparkles className="inline mr-2 h-6 w-6" />
                  Impacto Global
                </h3>
                <div className="space-y-4 text-center">
                  <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 p-4 rounded-xl">
                    <div className="text-3xl font-bold text-green-400 mb-1">+40%</div>
                    <div className="text-sm text-gray-300">Eficiência Energética</div>
                  </div>
                  <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 p-4 rounded-xl">
                    <div className="text-3xl font-bold text-blue-400 mb-1">-60%</div>
                    <div className="text-sm text-gray-300">Desperdício</div>
                  </div>
                  <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 p-4 rounded-xl">
                    <div className="text-3xl font-bold text-purple-400 mb-1">24/7</div>
                    <div className="text-sm text-gray-300">Monitoramento</div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

// Novo componente: Quantum Energy Grid
export const QuantumEnergyGrid = () => {
  const [activeNodes, setActiveNodes] = useState<number[]>([]);

  useEffect(() => {
    const interval = setInterval(() => {
      const newActiveNodes = Array.from({ length: Math.floor(Math.random() * 5) + 3 }, () =>
        Math.floor(Math.random() * 25)
      );
      setActiveNodes(newActiveNodes);
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <section className="py-20 bg-gradient-to-b from-gray-900 to-black relative overflow-hidden">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
            <Orbit className="inline mr-4 h-12 w-12 text-cyan-400" />
            Grid Quântico de Energia
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Rede descentralizada que conecta geradores e consumidores em tempo real através de tecnologia quântica
          </p>
        </motion.div>

        <div className="max-w-6xl mx-auto">
          <motion.div
            className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-lg border border-cyan-400/30 rounded-3xl p-8 shadow-2xl relative overflow-hidden"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            {/* Quantum Grid Visualization */}
            <div className="relative h-96 mb-8">
              <div className="absolute inset-0 grid grid-cols-5 gap-4 p-4">
                {Array.from({ length: 25 }).map((_, index) => (
                  <motion.div
                    key={index}
                    className={`relative rounded-lg border-2 ${
                      activeNodes.includes(index)
                        ? 'border-cyan-400 bg-cyan-400/20'
                        : 'border-gray-600 bg-gray-700/30'
                    } flex items-center justify-center`}
                    animate={activeNodes.includes(index) ? {
                      boxShadow: [
                        "0 0 0px rgba(34, 211, 238, 0)",
                        "0 0 20px rgba(34, 211, 238, 0.8)",
                        "0 0 0px rgba(34, 211, 238, 0)"
                      ]
                    } : {}}
                    transition={{ duration: 1.5, repeat: activeNodes.includes(index) ? Infinity : 0 }}
                  >
                    <motion.div
                      className={`w-4 h-4 rounded-full ${
                        activeNodes.includes(index) ? 'bg-cyan-400' : 'bg-gray-500'
                      }`}
                      animate={activeNodes.includes(index) ? {
                        scale: [1, 1.5, 1],
                        opacity: [1, 0.5, 1]
                      } : {}}
                      transition={{ duration: 1, repeat: activeNodes.includes(index) ? Infinity : 0 }}
                    />

                    {/* Quantum Connections */}
                    {activeNodes.includes(index) && (
                      <motion.div
                        className="absolute inset-0 border-2 border-cyan-400 rounded-lg"
                        initial={{ scale: 1, opacity: 1 }}
                        animate={{ scale: 2, opacity: 0 }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                    )}
                  </motion.div>
                ))}
              </div>

              {/* Energy Flow Lines */}
              <svg className="absolute inset-0 w-full h-full pointer-events-none">
                {activeNodes.map((nodeIndex, i) => {
                  const nextNode = activeNodes[i + 1] || activeNodes[0];
                  const x1 = (nodeIndex % 5) * 20 + 10;
                  const y1 = Math.floor(nodeIndex / 5) * 20 + 10;
                  const x2 = (nextNode % 5) * 20 + 10;
                  const y2 = Math.floor(nextNode / 5) * 20 + 10;

                  return (
                    <motion.line
                      key={`${nodeIndex}-${nextNode}`}
                      x1={`${x1}%`}
                      y1={`${y1}%`}
                      x2={`${x2}%`}
                      y2={`${y2}%`}
                      stroke="url(#quantumGradient)"
                      strokeWidth="2"
                      initial={{ pathLength: 0, opacity: 0 }}
                      animate={{ pathLength: 1, opacity: 0.8 }}
                      transition={{ duration: 1.5 }}
                    />
                  );
                })}
                <defs>
                  <linearGradient id="quantumGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#22d3ee" />
                    <stop offset="100%" stopColor="#3b82f6" />
                  </linearGradient>
                </defs>
              </svg>
            </div>

            {/* Grid Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-cyan-400 mb-2">
                  {activeNodes.length}
                </div>
                <div className="text-gray-300">Nós Ativos</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-400 mb-2">
                  {(activeNodes.length * 1.2).toFixed(1)}MW
                </div>
                <div className="text-gray-300">Energia Fluindo</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-400 mb-2">
                  99.9%
                </div>
                <div className="text-gray-300">Uptime</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-400 mb-2">
                  <1ms
                </div>
                <div className="text-gray-300">Latência</div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

// Novo componente: Futuristic Dashboard
export const FuturisticDashboard = () => {
  const [metrics, setMetrics] = useState({
    energyGenerated: 0,
    carbonSaved: 0,
    tokensCirculating: 0,
    globalUsers: 0
  });

  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(prev => ({
        energyGenerated: prev.energyGenerated + Math.random() * 10,
        carbonSaved: prev.carbonSaved + Math.random() * 5,
        tokensCirculating: prev.tokensCirculating + Math.random() * 1000,
        globalUsers: prev.globalUsers + Math.floor(Math.random() * 3)
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  return (
    <section className="py-20 bg-gradient-to-b from-black to-gray-900 relative overflow-hidden">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-5xl font-bold mb-6 bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
            <Layers className="inline mr-4 h-12 w-12 text-green-400" />
            Dashboard do Futuro
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Monitoramento em tempo real do impacto global da revolução energética
          </p>
        </motion.div>

        <div className="max-w-7xl mx-auto">
          <motion.div
            className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-lg border border-green-400/30 rounded-3xl p-8 shadow-2xl relative overflow-hidden"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            {/* Holographic Grid Background */}
            <div className="absolute inset-0 opacity-10">
              <div className="grid grid-cols-20 grid-rows-20 h-full w-full">
                {Array.from({ length: 400 }).map((_, i) => (
                  <motion.div
                    key={i}
                    className="border border-green-400/20"
                    animate={{ opacity: [0.1, 0.3, 0.1] }}
                    transition={{ duration: 4, repeat: Infinity, delay: Math.random() * 4 }}
                  />
                ))}
              </div>
            </div>

            {/* Main Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12 relative z-10">
              <motion.div
                className="bg-gradient-to-br from-green-500/20 to-emerald-600/20 backdrop-blur-sm border border-green-400/30 rounded-2xl p-6 text-center"
                whileHover={{ scale: 1.05, boxShadow: "0 0 30px rgba(34, 197, 94, 0.3)" }}
                transition={{ duration: 0.3 }}
              >
                <Zap className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <div className="text-3xl font-bold text-green-400 mb-2">
                  {metrics.energyGenerated.toFixed(1)}GWh
                </div>
                <div className="text-gray-300">Energia Limpa Gerada</div>
                <motion.div
                  className="mt-2 h-1 bg-green-400 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: "100%" }}
                  transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
                />
              </motion.div>

              <motion.div
                className="bg-gradient-to-br from-blue-500/20 to-cyan-600/20 backdrop-blur-sm border border-blue-400/30 rounded-2xl p-6 text-center"
                whileHover={{ scale: 1.05, boxShadow: "0 0 30px rgba(59, 130, 246, 0.3)" }}
                transition={{ duration: 0.3 }}
              >
                <Globe className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <div className="text-3xl font-bold text-blue-400 mb-2">
                  {metrics.carbonSaved.toFixed(1)}kt
                </div>
                <div className="text-gray-300">CO₂ Evitado</div>
                <motion.div
                  className="mt-2 h-1 bg-blue-400 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: "100%" }}
                  transition={{ duration: 2.5, repeat: Infinity, repeatType: "reverse" }}
                />
              </motion.div>

              <motion.div
                className="bg-gradient-to-br from-purple-500/20 to-pink-600/20 backdrop-blur-sm border border-purple-400/30 rounded-2xl p-6 text-center"
                whileHover={{ scale: 1.05, boxShadow: "0 0 30px rgba(147, 51, 234, 0.3)" }}
                transition={{ duration: 0.3 }}
              >
                <Coins className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                <div className="text-3xl font-bold text-purple-400 mb-2">
                  {(metrics.tokensCirculating / 1000).toFixed(1)}M
                </div>
                <div className="text-gray-300">Tokens ERNC</div>
                <motion.div
                  className="mt-2 h-1 bg-purple-400 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: "100%" }}
                  transition={{ duration: 3, repeat: Infinity, repeatType: "reverse" }}
                />
              </motion.div>

              <motion.div
                className="bg-gradient-to-br from-yellow-500/20 to-orange-600/20 backdrop-blur-sm border border-yellow-400/30 rounded-2xl p-6 text-center"
                whileHover={{ scale: 1.05, boxShadow: "0 0 30px rgba(234, 179, 8, 0.3)" }}
                transition={{ duration: 0.3 }}
              >
                <Network className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
                <div className="text-3xl font-bold text-yellow-400 mb-2">
                  {metrics.globalUsers.toLocaleString()}
                </div>
                <div className="text-gray-300">Usuários Globais</div>
                <motion.div
                  className="mt-2 h-1 bg-yellow-400 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: "100%" }}
                  transition={{ duration: 1.5, repeat: Infinity, repeatType: "reverse" }}
                />
              </motion.div>
            </div>

            {/* Real-time Activity Feed */}
            <motion.div
              className="bg-gray-800/50 backdrop-blur-sm border border-gray-600/30 rounded-2xl p-6"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.5 }}
            >
              <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
                <Activity className="mr-3 h-6 w-6 text-green-400" />
                Atividade em Tempo Real
              </h3>
              <div className="space-y-3">
                {[
                  { action: "Nova usina solar conectada", location: "São Paulo, BR", time: "agora", color: "green" },
                  { action: "Transação de 1.2MW completada", location: "Berlin, DE", time: "2s", color: "blue" },
                  { action: "Staking de 50k ERNC iniciado", location: "Tokyo, JP", time: "15s", color: "purple" },
                  { action: "Economia de 2.3t CO₂ registrada", location: "London, UK", time: "32s", color: "yellow" }
                ].map((activity, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <div className="flex items-center gap-3">
                      <motion.div
                        className={`w-3 h-3 rounded-full bg-${activity.color}-400`}
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 2, repeat: Infinity, delay: index * 0.5 }}
                      />
                      <div>
                        <div className="text-white font-medium">{activity.action}</div>
                        <div className="text-gray-400 text-sm">{activity.location}</div>
                      </div>
                    </div>
                    <div className="text-gray-400 text-sm">{activity.time}</div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};
