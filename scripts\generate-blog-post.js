// 🤖 Gerador Simples de Blog Post - FreeEnergy
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Keywords de alto impacto
const keywords = [
  "energia solar preço 2024",
  "instalação energia solar residencial", 
  "como funciona energia solar",
  "vantagens energia solar",
  "financiamento energia solar",
  "energia solar vale a pena",
  "painéis solares tipos",
  "energia solar apartamento"
];

// Função para gerar novo post
function generateNewPost() {
  const keyword = keywords[Math.floor(Math.random() * keywords.length)];
  const slug = keyword.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, '');
  
  const newPost = {
    title: `${keyword.charAt(0).toUpperCase() + keyword.slice(1)} | G<PERSON>a <PERSON> 2024`,
    description: `Tudo sobre ${keyword}. G<PERSON><PERSON> comple<PERSON>, preços atualizados e dicas de especialistas. Economize até 95% na conta de luz!`,
    slug: slug,
    date: new Date().toISOString().split('T')[0],
    author: "FreeEnergy Brasil",
    category: "Energia Solar",
    tags: [keyword, "energia solar", "economia", "sustentabilidade"],
    image: `https://images.unsplash.com/photo-${Math.floor(Math.random() * 1000000000)}?w=800&h=400&fit=crop&q=80`,
    readTime: Math.floor(Math.random() * 5) + 5,
    content: `# ${keyword.charAt(0).toUpperCase() + keyword.slice(1)}\n\nConteúdo completo sobre ${keyword}...`
  };
  
  return newPost;
}

// Atualizar posts.json
function updateBlogPosts() {
  const postsPath = path.join(__dirname, '../client/public/blog/posts.json');
  
  let posts = [];
  if (fs.existsSync(postsPath)) {
    posts = JSON.parse(fs.readFileSync(postsPath, 'utf8'));
  }
  
  // Gerar novo post
  const newPost = generateNewPost();
  
  // Adicionar no início
  posts.unshift(newPost);
  
  // Manter apenas 20 posts
  posts = posts.slice(0, 20);
  
  // Salvar
  fs.writeFileSync(postsPath, JSON.stringify(posts, null, 2));
  
  console.log(`✅ Novo post gerado: ${newPost.title}`);
  console.log(`🔗 Slug: ${newPost.slug}`);
  
  return newPost;
}

// Executar
if (process.argv[2] === 'generate') {
  updateBlogPosts();
}

export { updateBlogPosts, generateNewPost };
