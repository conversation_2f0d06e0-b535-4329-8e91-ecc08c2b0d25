/**
 * 📖 VISUALIZADOR DE ARTIGOS GERADOS - FREEENERGY
 * Mostra artigos completos gerados pelo AutoBlogger
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Eye, Calendar, Clock, Tag, User, ExternalLink } from 'lucide-react';
import RealPublisher from '../services/realPublisher';

const ArticleViewer = () => {
  const [articles, setArticles] = useState([]);
  const [selectedArticle, setSelectedArticle] = useState(null);
  const [loading, setLoading] = useState(true);

  const realPublisher = new RealPublisher();

  useEffect(() => {
    loadArticles();
  }, []);

  const loadArticles = () => {
    try {
      const posts = realPublisher.getExistingPosts();
      setArticles(posts);
      console.log(`📖 ${posts.length} artigos carregados`);
    } catch (error) {
      console.error('❌ Erro ao carregar artigos:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    try {
      return new Date(dateString).toLocaleDateString('pt-BR');
    } catch {
      return dateString;
    }
  };

  const getContentPreview = (content) => {
    if (!content) return 'Sem conteúdo disponível';
    
    // Remove HTML tags para preview
    const textContent = content.replace(/<[^>]*>/g, '');
    return textContent.length > 200 ? textContent.substring(0, 200) + '...' : textContent;
  };

  const getContentWordCount = (content) => {
    if (!content) return 0;
    const textContent = content.replace(/<[^>]*>/g, '');
    return textContent.split(/\s+/).filter(word => word.length > 0).length;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando artigos...</p>
        </div>
      </div>
    );
  }

  if (selectedArticle) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto p-6">
          {/* Header do Artigo */}
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <button
              onClick={() => setSelectedArticle(null)}
              className="mb-4 text-blue-600 hover:text-blue-700"
            >
              ← Voltar para lista
            </button>
            
            <h1 className="text-3xl font-bold text-gray-800 mb-4">
              {selectedArticle.title}
            </h1>
            
            <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-6">
              <div className="flex items-center gap-1">
                <User className="w-4 h-4" />
                {selectedArticle.author}
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                {formatDate(selectedArticle.date)}
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {selectedArticle.readTime} min de leitura
              </div>
              <div className="flex items-center gap-1">
                <Eye className="w-4 h-4" />
                {getContentWordCount(selectedArticle.content)} palavras
              </div>
            </div>

            {selectedArticle.tags && (
              <div className="flex flex-wrap gap-2 mb-6">
                {selectedArticle.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            )}
          </div>

          {/* Conteúdo do Artigo */}
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div 
              className="prose prose-lg max-w-none"
              dangerouslySetInnerHTML={{ __html: selectedArticle.content || '<p>Conteúdo não disponível</p>' }}
            />
          </div>

          {/* Ações */}
          <div className="bg-white rounded-lg shadow-lg p-6 mt-6">
            <h3 className="text-lg font-semibold mb-4">Ações</h3>
            <div className="flex gap-4">
              <a
                href="/blog"
                target="_blank"
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <ExternalLink className="w-4 h-4" />
                Ver no Blog
              </a>
              <button
                onClick={() => {
                  navigator.clipboard.writeText(selectedArticle.content);
                  alert('Conteúdo copiado!');
                }}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                Copiar HTML
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto p-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            📖 Artigos Gerados
          </h1>
          <p className="text-gray-600">
            {articles.length} artigos gerados automaticamente pelo sistema
          </p>
        </div>

        {articles.length === 0 ? (
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="text-6xl mb-4">📝</div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">
              Nenhum artigo gerado ainda
            </h2>
            <p className="text-gray-600 mb-6">
              Use o AutoBlogger para gerar seus primeiros artigos automaticamente
            </p>
            <a
              href="/autoblogger"
              className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Ir para AutoBlogger
            </a>
          </div>
        ) : (
          <div className="grid gap-6">
            {articles.map((article, index) => (
              <motion.div
                key={article.id || index}
                className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h2 className="text-xl font-semibold text-gray-800 mb-2">
                      {article.title}
                    </h2>
                    <p className="text-gray-600 mb-4">
                      {article.description || getContentPreview(article.content)}
                    </p>
                  </div>
                  {article.viral && (
                    <span className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
                      🔥 Viral
                    </span>
                  )}
                </div>

                <div className="flex flex-wrap gap-4 text-sm text-gray-500 mb-4">
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    {formatDate(article.date)}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    {article.readTime} min
                  </div>
                  <div className="flex items-center gap-1">
                    <Tag className="w-4 h-4" />
                    {article.category}
                  </div>
                  <div className="flex items-center gap-1">
                    <Eye className="w-4 h-4" />
                    {getContentWordCount(article.content)} palavras
                  </div>
                </div>

                {article.tags && (
                  <div className="flex flex-wrap gap-2 mb-4">
                    {article.tags.slice(0, 3).map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs"
                      >
                        #{tag}
                      </span>
                    ))}
                    {article.tags.length > 3 && (
                      <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                        +{article.tags.length - 3} mais
                      </span>
                    )}
                  </div>
                )}

                <div className="flex gap-3">
                  <button
                    onClick={() => setSelectedArticle(article)}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    <Eye className="w-4 h-4" />
                    Ver Completo
                  </button>
                  <a
                    href="/blog"
                    target="_blank"
                    className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    <ExternalLink className="w-4 h-4" />
                    Ver no Blog
                  </a>
                </div>
              </motion.div>
            ))}
          </div>
        )}

        <div className="mt-8 text-center">
          <button
            onClick={loadArticles}
            className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            🔄 Atualizar Lista
          </button>
        </div>
      </div>
    </div>
  );
};

export default ArticleViewer;
