import { motion } from 'framer-motion';
import { Network, Coins, Zap, Globe, ArrowRight, Cpu, Database, TrendingUp } from 'lucide-react';
import { useLocation } from 'wouter';

const FutureSection = () => {
  const [, setLocation] = useLocation();

  const handleEnercyClick = (e?: React.MouseEvent) => {
    e?.preventDefault();
    e?.stopPropagation();
    console.log('🚀 Navegando para ENERCY landing page...');
    console.log('Current location:', window.location.href);

    // Usar window.location diretamente para garantir que funcione
    window.location.href = window.location.origin + '/enercy';
  };

  const systemSteps = [
    {
      icon: <Network className="h-8 w-8 text-white" />,
      title: "Conexão Legal",
      description: "Acordos formais com usinas e cooperativas, assumindo a posse dos créditos de energia limpa gerada",
      bgColor: "#2ECC71",
      number: "01"
    },
    {
      icon: <Coins className="h-8 w-8 text-white" />,
      title: "Tokenização ERNC",
      description: "Cada kWh convertido em tokens ERNC rastreáveis na blockchain, criando ativos digitais de energia",
      bgColor: "#FFC107",
      number: "02"
    },
    {
      icon: <Zap className="h-8 w-8 text-white" />,
      title: "Distribuição Inteligente",
      description: "Clientes migram sem obra nem investimento, pagando com desconto pela liquidez do sistema",
      bgColor: "#3498DB",
      number: "03"
    },
    {
      icon: <TrendingUp className="h-8 w-8 text-white" />,
      title: "Staking Energético",
      description: "Investidores fazem staking de tokens ERNC, financiando operações e ganhando nas transações",
      bgColor: "#9B59B6",
      number: "04"
    }
  ];

  const impacts = [
    {
      icon: <Globe className="h-10 w-10 text-[#2ECC71]" />,
      title: "Democratização Global",
      description: "Acesso à energia limpa sem instalações, conectando qualquer país ou consumidor"
    },
    {
      icon: <Database className="h-10 w-10 text-[#2ECC71]" />,
      title: "Infraestrutura Digital",
      description: "Sistema operacional que interconecta, regula e liquida energia via blockchain"
    },
    {
      icon: <Cpu className="h-10 w-10 text-[#2ECC71]" />,
      title: "Empresa de Domínio",
      description: "Controla dados, liquidez e experiência - como Google ou Ethereum em seus setores"
    }
  ];

  return (
    <section id="o-futuro" className="py-20 bg-gradient-to-b from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Título Principal */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold font-montserrat mb-4">
            O Futuro da Energia: <span className="text-[#2ECC71]">ENERCY</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-6">
            O Sistema Operacional da Nova Energia
          </p>
          <div className="bg-[#2ECC71] bg-opacity-10 p-6 rounded-xl max-w-4xl mx-auto">
            <p className="text-lg text-gray-700 mb-6">
              A ENERCY não é apenas uma empresa de energia: é a <span className="font-semibold text-[#2ECC71]">infraestrutura digital</span> que
              interconecta, regula, liquida e democratiza o acesso à energia limpa via blockchain.
              Como <span className="font-semibold">Ethereum para finanças</span>, ENERCY é para energia.
            </p>
            <div className="text-center">
              <motion.button
                onClick={(e) => handleEnercyClick(e)}
                className="inline-flex items-center gap-2 bg-[#2ECC71] text-white font-bold py-3 px-8 rounded-lg shadow-lg hover:bg-[#27AE60] transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                🚀 Descobrir ENERCY <ArrowRight className="h-5 w-5" />
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Bloco 1: Mais que Economia, uma Revolução */}
        <div className="mb-20">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h3 className="text-2xl font-bold font-montserrat mb-4">
              Mais que Economia, uma <span className="text-[#2ECC71]">Revolução</span>
            </h3>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {impacts.map((impact, index) => (
              <motion.div
                key={index}
                className="bg-white rounded-xl p-6 shadow-lg text-center hover:shadow-xl transition-all duration-300"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.2 }}
              >
                <div className="flex justify-center mb-4">
                  {impact.icon}
                </div>
                <h4 className="text-xl font-bold mb-3">{impact.title}</h4>
                <p className="text-gray-600">{impact.description}</p>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Bloco 2: Como Funciona o Sistema */}
        <div className="mb-20">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h3 className="text-2xl font-bold font-montserrat mb-4">
              Como Funciona o <span className="text-[#2ECC71]">Sistema ENERCY</span>
            </h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Tokenização de energia, staking energético e liquidez de créditos em uma infraestrutura blockchain
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {systemSteps.map((step, index) => (
              <motion.div
                key={index}
                className="bg-white rounded-xl p-6 shadow-lg relative overflow-hidden group hover:shadow-xl transition-all duration-300"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <div
                  className="absolute -right-4 -top-4 w-20 h-20 rounded-full flex items-end justify-end pb-2 pr-2 text-white font-bold text-lg"
                  style={{ backgroundColor: step.bgColor }}
                >
                  {step.number}
                </div>
                <div className="flex flex-col h-full">
                  <div
                    className="w-16 h-16 rounded-full flex items-center justify-center mb-6"
                    style={{ backgroundColor: step.bgColor }}
                  >
                    {step.icon}
                  </div>
                  <h4 className="text-lg font-bold mb-4">{step.title}</h4>
                  <p className="text-gray-600 text-sm flex-grow">{step.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Bloco 3: Impacto Global */}
        <motion.div
          className="bg-gradient-to-r from-[#2ECC71] to-[#27AE60] rounded-xl p-8 text-white text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h3 className="text-2xl font-bold font-montserrat mb-4">
            O Ethereum da Energia Limpa
          </h3>
          <p className="text-lg mb-6 max-w-3xl mx-auto opacity-90">
            A ENERCY é o sistema que pode conectar qualquer país, usina ou consumidor ao futuro da energia. 
            Ao controlar o sistema, não a infraestrutura, ela se posiciona como empresa de domínio global, 
            criando um mercado paralelo de créditos com lastro real e rastreável.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.button
              onClick={(e) => handleEnercyClick(e)}
              className="inline-flex items-center gap-2 bg-white text-[#2ECC71] font-bold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Fazer parte do futuro <ArrowRight className="h-5 w-5" />
            </motion.button>
            <motion.button
              onClick={(e) => handleEnercyClick(e)}
              className="inline-flex items-center gap-2 border-2 border-white text-white font-bold py-3 px-6 rounded-lg hover:bg-white hover:text-[#2ECC71] transition-all duration-300 cursor-pointer"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Entender o sistema
            </motion.button>
          </div>
        </motion.div>

        {/* Modelo de Negócio */}
        <motion.div
          className="mt-16 bg-gray-50 rounded-xl p-8"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h4 className="text-xl font-bold text-center mb-6">
            Modelo de <span className="text-[#2ECC71]">Domínio Energético</span>
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 text-center">
            <div className="p-4">
              <div className="text-2xl font-bold text-[#2ECC71] mb-2">Intermediação</div>
              <p className="text-sm text-gray-600">Receita por transações energéticas</p>
            </div>
            <div className="p-4">
              <div className="text-2xl font-bold text-[#FFC107] mb-2">Staking</div>
              <p className="text-sm text-gray-600">Operação de staking energético</p>
            </div>
            <div className="p-4">
              <div className="text-2xl font-bold text-[#3498DB] mb-2">Licenciamento</div>
              <p className="text-sm text-gray-600">Infraestrutura digital B2B</p>
            </div>
            <div className="p-4">
              <div className="text-2xl font-bold text-[#9B59B6] mb-2">Fidelização</div>
              <p className="text-sm text-gray-600">Consumidores em escala massiva</p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default FutureSection;
