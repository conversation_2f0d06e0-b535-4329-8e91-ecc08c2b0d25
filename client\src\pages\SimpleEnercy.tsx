import { useLocation } from 'wouter';
import { useState, useEffect, useRef } from 'react';

const SimpleEnercy = () => {
  const [, setLocation] = useLocation();
  const [isLoaded, setIsLoaded] = useState(false);
  const [particles, setParticles] = useState<Array<{id: number, x: number, y: number, delay: number}>>([]);
  const [currentPhase, setCurrentPhase] = useState(0);
  const [tokenPrice, setTokenPrice] = useState(0.45);
  const [energyGenerated, setEnergyGenerated] = useState(1247.8);
  const [activeSection, setActiveSection] = useState('hero');
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [scrollY, setScrollY] = useState(0);
  const [visibleSections, setVisibleSections] = useState<Set<string>>(new Set());
  const [investmentAmount, setInvestmentAmount] = useState(2500);
  const [roiCalculation, setRoiCalculation] = useState({ monthly: 0, yearly: 0, tokens: 0 });
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const observerRef = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    setIsLoaded(true);

    // Partículas otimizadas - reduzidas para performance
    const newParticles = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      delay: Math.random() * 3
    }));
    setParticles(newParticles);

    // Scroll tracking simplificado
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    // Intersection Observer para animações de entrada
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setVisibleSections(prev => new Set([...prev, entry.target.id]));
          }
        });
      },
      { threshold: 0.1 }
    );

    // Observar todas as seções
    const sections = document.querySelectorAll('section[id]');
    sections.forEach(section => {
      if (observerRef.current) {
        observerRef.current.observe(section);
      }
    });

    window.addEventListener('scroll', handleScroll, { passive: true });

    // Simular dados em tempo real otimizado
    const interval = setInterval(() => {
      setTokenPrice(prev => {
        const change = (Math.random() - 0.5) * 0.01;
        return Math.max(0.1, prev + change);
      });
      setEnergyGenerated(prev => prev + Math.random() * 2 + 0.5);
    }, 3000);

    return () => {
      clearInterval(interval);
      window.removeEventListener('scroll', handleScroll);
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  // Calcular ROI baseado no investimento
  useEffect(() => {
    const tokens = investmentAmount / tokenPrice;
    const monthlyReturn = (tokens * 0.15) / 12; // 15% APY
    const yearlyReturn = tokens * 0.15;

    setRoiCalculation({
      tokens: Math.floor(tokens),
      monthly: monthlyReturn * tokenPrice,
      yearly: yearlyReturn * tokenPrice
    });
  }, [investmentAmount, tokenPrice]);

  const handleBackToHome = () => {
    setLocation('/');
  };

  const scrollToSection = (sectionId: string) => {
    setActiveSection(sectionId);
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-black text-white relative overflow-hidden">
      {/* Background Futurístico Avançado */}
      <div className="fixed inset-0 bg-gradient-to-br from-black via-gray-900 to-green-900 overflow-hidden">
        {/* Background Layer Simplificado */}
        <div className="absolute inset-0 bg-gradient-to-r from-green-500/3 to-blue-500/3"></div>

        {/* Partículas de Energia Simplificadas */}
        {particles.map((particle) => (
          <div
            key={particle.id}
            className="absolute rounded-full"
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              width: '3px',
              height: '3px',
              background: particle.id % 3 === 0 ? '#10b981' :
                         particle.id % 3 === 1 ? '#3b82f6' : '#8b5cf6',
              animation: `particle-float ${10 + particle.delay}s ease-in-out infinite ${particle.delay}s`,
              opacity: 0.6
            }}
          />
        ))}

        {/* Grid Quântico Simplificado */}
        <div className="absolute inset-0 opacity-5">
          <div
            className="grid grid-cols-8 md:grid-cols-12 grid-rows-8 md:grid-rows-12 h-full w-full"
            style={{
              transform: `translate3d(0, ${scrollY * 0.02}px, 0)`
            }}
          >
            {Array.from({ length: window.innerWidth > 768 ? 144 : 64 }).map((_, i) => (
              <div
                key={i}
                className="border border-green-400/10"
                style={{
                  borderColor: i % 12 === 0 ? 'rgba(59, 130, 246, 0.15)' : 'rgba(34, 197, 94, 0.08)'
                }}
              />
            ))}
          </div>
        </div>

        {/* Orbes de Energia Simplificadas */}
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-green-500/5 rounded-full blur-2xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-blue-500/5 rounded-full blur-2xl"></div>
        <div className="absolute top-1/2 left-1/2 w-48 h-48 bg-purple-500/5 rounded-full blur-xl transform -translate-x-1/2 -translate-y-1/2"></div>

        {/* Efeito de Fundo Sutil */}
      </div>

      {/* Header Mobile-First */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-xl border-b border-green-400/20">
        <div className="container mx-auto px-4 sm:px-6 py-3 sm:py-4">
          <div className="flex justify-between items-center">
            <button
              onClick={handleBackToHome}
              className="flex items-center gap-1 sm:gap-2 text-green-400 hover:text-green-300 transition-colors group min-h-[44px]"
            >
              <span className="group-hover:-translate-x-1 transition-transform text-lg sm:text-base">←</span>
              <span className="font-semibold text-sm sm:text-base">Free Energy</span>
            </button>

            <button
              onClick={handleBackToHome}
              className="text-xl sm:text-2xl md:text-3xl font-black hover:scale-105 transition-transform duration-300"
            >
              <span className="bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 bg-clip-text text-transparent animate-pulse">
                ENERCY
              </span>
            </button>

            <nav className="hidden lg:flex space-x-6">
              {[
                { name: 'Produto', id: 'produto' },
                { name: 'Roadmap', id: 'roadmap' },
                { name: 'Tokenomics', id: 'tokenomics' },
                { name: 'Calculadora', id: 'calculadora' },
                { name: 'FAQ', id: 'faq' }
              ].map((item) => (
                <button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  className={`text-gray-300 hover:text-green-400 transition-colors font-medium relative ${
                    activeSection === item.id ? 'text-green-400' : ''
                  }`}
                >
                  {item.name}
                  {activeSection === item.id && (
                    <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-green-400 rounded-full"></div>
                  )}
                </button>
              ))}
            </nav>

            <div className="flex items-center gap-2 sm:gap-4">
              <button className="hidden md:block px-4 md:px-6 py-2 md:py-3 bg-gradient-to-r from-green-500 to-blue-600 rounded-full font-bold text-xs md:text-sm hover:shadow-lg hover:shadow-green-500/50 transition-all duration-300 hover:scale-105">
                ⚡ Investir Agora
              </button>

              {/* Menu Mobile Touch-Friendly */}
              <button
                className="lg:hidden text-green-400 p-3 min-h-[44px] min-w-[44px] flex items-center justify-center"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                <div className="w-6 h-6 flex flex-col justify-center space-y-1">
                  <div className={`h-0.5 w-full bg-current transition-all duration-300 ${mobileMenuOpen ? 'rotate-45 translate-y-1.5' : ''}`}></div>
                  <div className={`h-0.5 w-full bg-current transition-all duration-300 ${mobileMenuOpen ? 'opacity-0' : ''}`}></div>
                  <div className={`h-0.5 w-full bg-current transition-all duration-300 ${mobileMenuOpen ? '-rotate-45 -translate-y-1.5' : ''}`}></div>
                </div>
              </button>
            </div>
          </div>

          {/* Menu Mobile Dropdown Touch-Optimized */}
          {mobileMenuOpen && (
            <div className="lg:hidden bg-black/95 backdrop-blur-xl border-t border-green-400/20 animate-slide-down">
              <div className="container mx-auto px-4 sm:px-6 py-4">
                <nav className="flex flex-col space-y-2">
                  {[
                    { name: 'Produto', id: 'produto' },
                    { name: 'Roadmap', id: 'roadmap' },
                    { name: 'Tokenomics', id: 'tokenomics' },
                    { name: 'Calculadora', id: 'calculadora' },
                    { name: 'FAQ', id: 'faq' }
                  ].map((item) => (
                    <button
                      key={item.id}
                      onClick={() => {
                        scrollToSection(item.id);
                        setMobileMenuOpen(false);
                      }}
                      className="text-left text-gray-300 hover:text-green-400 transition-colors font-medium py-3 px-2 min-h-[44px] rounded-lg hover:bg-green-400/10"
                    >
                      {item.name}
                    </button>
                  ))}
                  <button
                    onClick={() => {
                      scrollToSection('investir');
                      setMobileMenuOpen(false);
                    }}
                    className="mt-4 px-6 py-3 bg-gradient-to-r from-green-500 to-blue-600 rounded-full font-bold text-sm text-center min-h-[44px] w-full"
                  >
                    ⚡ Investir Agora
                  </button>
                </nav>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* SEÇÃO O FUTURO - Espaço reservado para navegação da página inicial */}
      <section id="o-futuro" className="relative z-10 min-h-screen flex items-center justify-center pt-20">
        <div className="container mx-auto px-6 text-center">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-5xl md:text-6xl font-bold mb-8 bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent">
              🚀 O Futuro da Energia
            </h2>
            <p className="text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed">
              Bem-vindo ao futuro da energia! Esta seção será expandida com conteúdo específico
              sobre como a ENERCY está revolucionando o mercado energético global.
            </p>
            <button
              onClick={() => scrollToSection('hero')}
              className="px-8 py-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-full font-bold text-lg hover:scale-105 transition-transform"
            >
              🔋 Descobrir ENERCY
            </button>
          </div>
        </div>
      </section>

      {/* SEÇÃO HERO */}
      <section id="hero" className="relative z-10 min-h-screen flex items-center justify-center pt-20">
        <div className="container mx-auto px-6">
          <div className="text-center max-w-6xl mx-auto">
            <div className={`transition-all duration-1500 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-20'}`}>
              {/* Título Principal */}
              <h1 className="text-8xl md:text-9xl lg:text-[12rem] font-black mb-8 bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 bg-clip-text text-transparent relative">
                ENERCY
                <div className="absolute inset-0 text-8xl md:text-9xl lg:text-[12rem] font-black bg-gradient-to-r from-green-400/20 via-blue-500/20 to-purple-600/20 bg-clip-text text-transparent blur-sm animate-pulse"></div>
              </h1>

              {/* Subtítulo */}
              <div className="text-3xl md:text-5xl mb-8 font-light">
                <span className="text-green-400">Energia Limpa</span> como <span className="text-blue-400">Ativo Digital</span>
              </div>

              {/* Descrição Principal */}
              <p className="text-xl md:text-2xl mb-12 text-gray-300 max-w-4xl mx-auto leading-relaxed">
                A primeira plataforma que <span className="text-green-400 font-bold">tokeniza energia renovável</span>,
                criando um mercado descentralizado onde você pode
                <span className="text-blue-400 font-bold"> investir, negociar e lucrar</span> com a transição energética global.
              </p>

              {/* Métricas em Tempo Real */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <div className="bg-gray-800/50 backdrop-blur-sm border border-green-400/30 rounded-2xl p-6">
                  <div className="text-3xl font-bold text-green-400 mb-2">
                    ${tokenPrice.toFixed(3)}
                  </div>
                  <div className="text-gray-300">Preço Token ERNC</div>
                  <div className="text-green-400 text-sm">+12.5% (24h)</div>
                </div>

                <div className="bg-gray-800/50 backdrop-blur-sm border border-blue-400/30 rounded-2xl p-6">
                  <div className="text-3xl font-bold text-blue-400 mb-2">
                    {energyGenerated.toFixed(1)} MWh
                  </div>
                  <div className="text-gray-300">Energia Tokenizada</div>
                  <div className="text-blue-400 text-sm">Tempo Real</div>
                </div>

                <div className="bg-gray-800/50 backdrop-blur-sm border border-purple-400/30 rounded-2xl p-6">
                  <div className="text-3xl font-bold text-purple-400 mb-2">
                    R$ 2.4M
                  </div>
                  <div className="text-gray-300">Volume Negociado</div>
                  <div className="text-purple-400 text-sm">Últimos 30 dias</div>
                </div>
              </div>

              {/* CTAs Principais */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
                <button
                  onClick={() => scrollToSection('investir')}
                  className="px-10 py-5 bg-gradient-to-r from-green-500 to-blue-600 rounded-full font-bold text-xl shadow-2xl hover:shadow-green-500/25 transition-all duration-300 hover:scale-105 relative overflow-hidden group"
                >
                  <span className="relative z-10">✨ Entrar no Futuro →</span>
                  <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-blue-500 opacity-0 group-hover:opacity-20 transition-opacity"></div>
                </button>

                <button
                  onClick={() => scrollToSection('produto')}
                  className="px-10 py-5 border-2 border-green-400 rounded-full font-bold text-xl hover:bg-green-400 hover:text-black transition-all duration-300 hover:scale-105"
                >
                  🔬 Ver Tecnologia
                </button>
              </div>

              {/* Indicador de Scroll */}
              <div className="animate-bounce">
                <div className="w-6 h-10 border-2 border-green-400 rounded-full flex justify-center mx-auto">
                  <div className="w-1 h-3 bg-green-400 rounded-full mt-2 animate-pulse"></div>
                </div>
                <p className="text-green-400 text-sm mt-2">Descubra o Futuro</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* SEÇÃO TOKENOMICS */}
      <section id="tokenomics" className="relative z-10 py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
              💰 Tokenomics ERNC
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Economia sustentável e transparente para o futuro da energia
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Gráfico de Distribuição */}
            <div className="bg-gray-800/50 backdrop-blur-sm border border-yellow-400/30 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-yellow-400 mb-6">Distribuição de Tokens</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-4 bg-green-500/20 rounded-lg">
                  <span>Produtores de Energia</span>
                  <span className="font-bold text-green-400">40%</span>
                </div>
                <div className="flex justify-between items-center p-4 bg-blue-500/20 rounded-lg">
                  <span>Investidores & Staking</span>
                  <span className="font-bold text-blue-400">25%</span>
                </div>
                <div className="flex justify-between items-center p-4 bg-purple-500/20 rounded-lg">
                  <span>Desenvolvimento</span>
                  <span className="font-bold text-purple-400">20%</span>
                </div>
                <div className="flex justify-between items-center p-4 bg-yellow-500/20 rounded-lg">
                  <span>Marketing & Parcerias</span>
                  <span className="font-bold text-yellow-400">10%</span>
                </div>
                <div className="flex justify-between items-center p-4 bg-red-500/20 rounded-lg">
                  <span>Reserva de Emergência</span>
                  <span className="font-bold text-red-400">5%</span>
                </div>
              </div>
            </div>

            {/* Métricas e Utilidades */}
            <div className="space-y-6">
              <div className="bg-gray-800/50 backdrop-blur-sm border border-orange-400/30 rounded-2xl p-6">
                <h4 className="text-xl font-bold text-orange-400 mb-4">📊 Métricas do Token</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-2xl font-bold text-white">1B</div>
                    <div className="text-gray-400">Supply Total</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-400">${tokenPrice.toFixed(3)}</div>
                    <div className="text-gray-400">Preço Atual</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-blue-400">15%</div>
                    <div className="text-gray-400">APY Staking</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-purple-400">24h</div>
                    <div className="text-gray-400">Lock Period</div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800/50 backdrop-blur-sm border border-green-400/30 rounded-2xl p-6">
                <h4 className="text-xl font-bold text-green-400 mb-4">⚡ Utilidades do Token</h4>
                <ul className="space-y-3 text-gray-300">
                  <li className="flex items-center gap-3">
                    <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                    Compra e venda de energia
                  </li>
                  <li className="flex items-center gap-3">
                    <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                    Staking para rewards
                  </li>
                  <li className="flex items-center gap-3">
                    <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                    Governança da plataforma
                  </li>
                  <li className="flex items-center gap-3">
                    <span className="w-2 h-2 bg-yellow-400 rounded-full"></span>
                    Desconto em taxas
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* SEÇÃO CALCULADORA ROI */}
      <section id="calculadora" className="relative z-10 py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
              🧮 Calculadora de ROI
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Descubra quanto você pode ganhar investindo em energia limpa
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="bg-gray-800/50 backdrop-blur-sm border border-yellow-400/30 rounded-2xl p-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Controles da Calculadora */}
                <div className="space-y-6">
                  <div>
                    <label className="block text-lg font-semibold text-yellow-400 mb-4">
                      Valor do Investimento
                    </label>
                    <div className="relative">
                      <input
                        type="range"
                        min="500"
                        max="50000"
                        step="500"
                        value={investmentAmount}
                        onChange={(e) => setInvestmentAmount(Number(e.target.value))}
                        className="w-full h-3 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                      />
                      <div className="flex justify-between text-sm text-gray-400 mt-2">
                        <span>R$ 500</span>
                        <span>R$ 50.000</span>
                      </div>
                    </div>
                    <div className="text-center mt-4">
                      <span className="text-3xl font-bold text-white">
                        R$ {investmentAmount.toLocaleString('pt-BR')}
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-700/50 p-4 rounded-lg text-center">
                      <div className="text-yellow-400 font-bold text-lg">
                        {roiCalculation.tokens.toLocaleString()}
                      </div>
                      <div className="text-gray-300 text-sm">Tokens ERNC</div>
                    </div>
                    <div className="bg-gray-700/50 p-4 rounded-lg text-center">
                      <div className="text-green-400 font-bold text-lg">
                        ${tokenPrice.toFixed(3)}
                      </div>
                      <div className="text-gray-300 text-sm">Preço por Token</div>
                    </div>
                  </div>
                </div>

                {/* Resultados do ROI */}
                <div className="space-y-6">
                  <h3 className="text-2xl font-bold text-white mb-4">Retornos Estimados</h3>

                  <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 p-6 rounded-lg border border-green-400/30">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-300">Retorno Mensal</span>
                      <span className="text-2xl font-bold text-green-400">
                        R$ {roiCalculation.monthly.toFixed(2)}
                      </span>
                    </div>
                    <div className="text-sm text-gray-400">
                      ~{((roiCalculation.monthly / investmentAmount) * 100).toFixed(2)}% do investimento
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 p-6 rounded-lg border border-blue-400/30">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-300">Retorno Anual</span>
                      <span className="text-2xl font-bold text-blue-400">
                        R$ {roiCalculation.yearly.toFixed(2)}
                      </span>
                    </div>
                    <div className="text-sm text-gray-400">
                      {((roiCalculation.yearly / investmentAmount) * 100).toFixed(1)}% APY
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 p-6 rounded-lg border border-purple-400/30">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-300">Em 5 Anos</span>
                      <span className="text-2xl font-bold text-purple-400">
                        R$ {(roiCalculation.yearly * 5).toFixed(2)}
                      </span>
                    </div>
                    <div className="text-sm text-gray-400">
                      Apenas com staking (sem valorização)
                    </div>
                  </div>

                  <button
                    onClick={() => scrollToSection('investir')}
                    className="w-full px-6 py-4 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-full font-bold text-lg hover:scale-105 transition-transform text-black"
                  >
                    💰 Investir R$ {investmentAmount.toLocaleString('pt-BR')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* SEÇÃO DEPOIMENTOS */}
      <section id="depoimentos" className="relative z-10 py-20 bg-gradient-to-b from-gray-900 to-black">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
              💬 O que dizem nossos investidores
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Histórias reais de quem já está lucrando com energia limpa
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {/* Depoimento 1 */}
            <div className="bg-gray-800/50 backdrop-blur-sm border border-green-400/30 rounded-2xl p-6 hover:border-green-400/60 transition-all duration-300 hover:scale-105">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                  M
                </div>
                <div className="ml-4">
                  <h4 className="font-bold text-white">Maria Silva</h4>
                  <p className="text-gray-400 text-sm">Investidora desde Q1 2024</p>
                </div>
              </div>
              <p className="text-gray-300 mb-4">
                "Em 6 meses já recuperei 8% do investimento só com staking. A plataforma é intuitiva e os retornos são consistentes."
              </p>
              <div className="flex items-center">
                <div className="flex text-yellow-400">
                  {'★'.repeat(5)}
                </div>
                <span className="ml-2 text-green-400 font-bold">+18.2% ROI</span>
              </div>
            </div>

            {/* Depoimento 2 */}
            <div className="bg-gray-800/50 backdrop-blur-sm border border-blue-400/30 rounded-2xl p-6 hover:border-blue-400/60 transition-all duration-300 hover:scale-105">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                  J
                </div>
                <div className="ml-4">
                  <h4 className="font-bold text-white">João Santos</h4>
                  <p className="text-gray-400 text-sm">Empresário, SP</p>
                </div>
              </div>
              <p className="text-gray-300 mb-4">
                "Diversifiquei meu portfólio com ERNC e não me arrependo. É o futuro dos investimentos sustentáveis."
              </p>
              <div className="flex items-center">
                <div className="flex text-yellow-400">
                  {'★'.repeat(5)}
                </div>
                <span className="ml-2 text-blue-400 font-bold">R$ 25k investidos</span>
              </div>
            </div>

            {/* Depoimento 3 */}
            <div className="bg-gray-800/50 backdrop-blur-sm border border-purple-400/30 rounded-2xl p-6 hover:border-purple-400/60 transition-all duration-300 hover:scale-105">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                  A
                </div>
                <div className="ml-4">
                  <h4 className="font-bold text-white">Ana Costa</h4>
                  <p className="text-gray-400 text-sm">Engenheira, RJ</p>
                </div>
              </div>
              <p className="text-gray-300 mb-4">
                "Como engenheira, entendo o potencial da tecnologia. ENERCY está revolucionando o setor energético."
              </p>
              <div className="flex items-center">
                <div className="flex text-yellow-400">
                  {'★'.repeat(5)}
                </div>
                <span className="ml-2 text-purple-400 font-bold">Expert Review</span>
              </div>
            </div>
          </div>

          {/* Social Proof */}
          <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 backdrop-blur-sm border border-green-400/30 rounded-2xl p-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold text-green-400 mb-2">1,247+</div>
                <div className="text-gray-300">Investidores Ativos</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-blue-400 mb-2">R$ 2.4M</div>
                <div className="text-gray-300">Volume Total</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-purple-400 mb-2">15.8%</div>
                <div className="text-gray-300">ROI Médio</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-yellow-400 mb-2">4.9/5</div>
                <div className="text-gray-300">Avaliação</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* SEÇÃO FAQ */}
      <section id="faq" className="relative z-10 py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent">
              ❓ Perguntas Frequentes
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Tire suas dúvidas sobre investimento em energia tokenizada
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-4">
            {[
              {
                q: "Como funciona a tokenização de energia?",
                a: "Cada token ERNC representa 1 MWh de energia renovável certificada. A energia é produzida por usinas parceiras e convertida em tokens na blockchain, garantindo transparência e rastreabilidade."
              },
              {
                q: "Qual é o risco do investimento?",
                a: "Como qualquer investimento, existe risco. Porém, a energia é uma commodity essencial com demanda crescente. Nosso modelo é baseado em contratos de longo prazo com usinas estabelecidas."
              },
              {
                q: "Como recebo os retornos?",
                a: "Os retornos são pagos mensalmente em tokens ERNC através do sistema de staking. Você pode converter para reais a qualquer momento através da plataforma."
              },
              {
                q: "Posso vender meus tokens a qualquer momento?",
                a: "Sim! Nossa plataforma oferece liquidez 24/7. Você pode vender seus tokens no marketplace ou diretamente para outros investidores."
              },
              {
                q: "A plataforma é regulamentada?",
                a: "Estamos em processo de regulamentação com a CVM e seguimos todas as normas de compliance. A tecnologia blockchain garante transparência total das operações."
              }
            ].map((faq, index) => (
              <div key={index} className="bg-gray-800/50 backdrop-blur-sm border border-gray-600/30 rounded-lg overflow-hidden">
                <button
                  className="w-full p-6 text-left hover:bg-gray-700/30 transition-colors"
                  onClick={() => {
                    const content = document.getElementById(`faq-${index}`);
                    if (content) {
                      content.style.display = content.style.display === 'none' ? 'block' : 'none';
                    }
                  }}
                >
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-semibold text-white">{faq.q}</h3>
                    <span className="text-green-400 text-xl">+</span>
                  </div>
                </button>
                <div id={`faq-${index}`} className="px-6 pb-6 text-gray-300" style={{ display: 'none' }}>
                  {faq.a}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* SEÇÃO INVESTIR */}
      <section id="investir" className="relative z-10 py-20 bg-gradient-to-b from-gray-900 to-black">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent">
              🚀 Invista no Futuro da Energia
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Seja parte da revolução energética e lucre com a transição para energia limpa
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              {/* Plano Básico */}
              <div className="bg-gray-800/50 backdrop-blur-sm border border-green-400/30 rounded-2xl p-8 hover:border-green-400/60 transition-all duration-300 hover:scale-105">
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-green-400 mb-4">⚡ Starter</h3>
                  <div className="text-4xl font-bold text-white mb-2">R$ 500</div>
                  <div className="text-gray-400 mb-6">Investimento Mínimo</div>
                  <ul className="space-y-3 text-left mb-8">
                    <li className="flex items-center gap-2">
                      <span className="text-green-400">✓</span>
                      1.000 tokens ERNC
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="text-green-400">✓</span>
                      Staking básico (10% APY)
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="text-green-400">✓</span>
                      Dashboard de investimentos
                    </li>
                  </ul>
                  <button className="w-full px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 rounded-full font-bold hover:scale-105 transition-transform">
                    Investir Agora
                  </button>
                </div>
              </div>

              {/* Plano Pro */}
              <div className="bg-gray-800/50 backdrop-blur-sm border border-blue-400/30 rounded-2xl p-8 hover:border-blue-400/60 transition-all duration-300 hover:scale-105 relative">
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-bold">
                  POPULAR
                </div>
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-blue-400 mb-4">🔥 Pro</h3>
                  <div className="text-4xl font-bold text-white mb-2">R$ 2.500</div>
                  <div className="text-gray-400 mb-6">Recomendado</div>
                  <ul className="space-y-3 text-left mb-8">
                    <li className="flex items-center gap-2">
                      <span className="text-blue-400">✓</span>
                      5.500 tokens ERNC
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="text-blue-400">✓</span>
                      Staking premium (15% APY)
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="text-blue-400">✓</span>
                      Acesso ao marketplace
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="text-blue-400">✓</span>
                      Relatórios avançados
                    </li>
                  </ul>
                  <button className="w-full px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full font-bold hover:scale-105 transition-transform">
                    Investir Agora
                  </button>
                </div>
              </div>

              {/* Plano Enterprise */}
              <div className="bg-gray-800/50 backdrop-blur-sm border border-purple-400/30 rounded-2xl p-8 hover:border-purple-400/60 transition-all duration-300 hover:scale-105">
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-purple-400 mb-4">👑 Enterprise</h3>
                  <div className="text-4xl font-bold text-white mb-2">R$ 10.000+</div>
                  <div className="text-gray-400 mb-6">Investidores Sérios</div>
                  <ul className="space-y-3 text-left mb-8">
                    <li className="flex items-center gap-2">
                      <span className="text-purple-400">✓</span>
                      25.000+ tokens ERNC
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="text-purple-400">✓</span>
                      Staking VIP (20% APY)
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="text-purple-400">✓</span>
                      Acesso à governança
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="text-purple-400">✓</span>
                      Suporte dedicado
                    </li>
                  </ul>
                  <button className="w-full px-6 py-3 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full font-bold hover:scale-105 transition-transform">
                    Falar com Especialista
                  </button>
                </div>
              </div>
            </div>

            {/* CTA Final */}
            <div className="text-center bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-sm border border-green-400/30 rounded-2xl p-12">
              <h3 className="text-3xl font-bold text-white mb-4">
                🌟 Não Perca a Oportunidade
              </h3>
              <p className="text-xl text-gray-300 mb-8">
                Seja um dos primeiros investidores na revolução da energia limpa
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="px-8 py-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-full font-bold text-lg hover:scale-105 transition-transform">
                  💰 Investir Agora
                </button>
                <button
                  onClick={handleBackToHome}
                  className="px-8 py-4 border-2 border-green-400 rounded-full font-bold text-lg hover:bg-green-400 hover:text-black transition-all duration-300"
                >
                  🏠 Voltar para Free Energy
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* SEÇÃO NEWSLETTER */}
      <section id="newsletter" className="relative z-10 py-20 bg-gradient-to-b from-gray-900 to-black">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-sm border border-green-400/30 rounded-2xl p-12">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent">
                📧 Fique por dentro das novidades
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                Receba atualizações exclusivas sobre o mercado de energia e oportunidades de investimento
              </p>

              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <input
                  type="email"
                  placeholder="Seu melhor e-mail"
                  className="flex-1 px-6 py-4 bg-gray-800/50 border border-gray-600 rounded-full text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-colors"
                />
                <button className="px-8 py-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-full font-bold hover:scale-105 transition-transform whitespace-nowrap">
                  ✨ Inscrever-se
                </button>
              </div>

              <p className="text-sm text-gray-400 mt-4">
                🔒 Seus dados estão seguros. Não enviamos spam.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* FOOTER */}
      <footer className="relative z-10 bg-black border-t border-gray-800">
        <div className="container mx-auto px-6 py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Logo e Descrição */}
            <div className="lg:col-span-2">
              <div className="text-3xl font-black mb-4">
                <span className="bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
                  ENERCY
                </span>
              </div>
              <p className="text-gray-300 mb-6 max-w-md">
                A primeira plataforma que tokeniza energia renovável, criando um mercado descentralizado
                para investir no futuro sustentável.
              </p>
              <div className="flex space-x-4">
                <button className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-green-500 transition-colors">
                  📱
                </button>
                <button className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-blue-500 transition-colors">
                  🐦
                </button>
                <button className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-purple-500 transition-colors">
                  💼
                </button>
                <button className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-red-500 transition-colors">
                  📺
                </button>
              </div>
            </div>

            {/* Links Rápidos */}
            <div>
              <h3 className="text-lg font-bold text-white mb-4">Plataforma</h3>
              <ul className="space-y-2">
                <li><button onClick={() => scrollToSection('produto')} className="text-gray-300 hover:text-green-400 transition-colors">Como Funciona</button></li>
                <li><button onClick={() => scrollToSection('tokenomics')} className="text-gray-300 hover:text-green-400 transition-colors">Tokenomics</button></li>
                <li><button onClick={() => scrollToSection('roadmap')} className="text-gray-300 hover:text-green-400 transition-colors">Roadmap</button></li>
                <li><button onClick={() => scrollToSection('calculadora')} className="text-gray-300 hover:text-green-400 transition-colors">Calculadora</button></li>
              </ul>
            </div>

            {/* Suporte */}
            <div>
              <h3 className="text-lg font-bold text-white mb-4">Suporte</h3>
              <ul className="space-y-2">
                <li><button onClick={() => scrollToSection('faq')} className="text-gray-300 hover:text-green-400 transition-colors">FAQ</button></li>
                <li><a href="#" className="text-gray-300 hover:text-green-400 transition-colors">Documentação</a></li>
                <li><a href="#" className="text-gray-300 hover:text-green-400 transition-colors">Suporte Técnico</a></li>
                <li><a href="#" className="text-gray-300 hover:text-green-400 transition-colors">Contato</a></li>
              </ul>
            </div>
          </div>

          {/* Métricas em Tempo Real */}
          <div className="border-t border-gray-800 mt-12 pt-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-green-400">{energyGenerated.toFixed(1)} MWh</div>
                <div className="text-gray-400 text-sm">Energia Tokenizada</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-400">${tokenPrice.toFixed(3)}</div>
                <div className="text-gray-400 text-sm">Preço ERNC</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-400">1,247+</div>
                <div className="text-gray-400 text-sm">Investidores</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-yellow-400">99.9%</div>
                <div className="text-gray-400 text-sm">Uptime</div>
              </div>
            </div>
          </div>

          {/* Copyright */}
          <div className="border-t border-gray-800 mt-8 pt-8 text-center">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 text-sm">
                © 2024 ENERCY. Todos os direitos reservados. Desenvolvido com 💚 para um futuro sustentável.
              </p>
              <div className="flex space-x-6 mt-4 md:mt-0">
                <a href="#" className="text-gray-400 hover:text-green-400 text-sm transition-colors">Termos de Uso</a>
                <a href="#" className="text-gray-400 hover:text-green-400 text-sm transition-colors">Privacidade</a>
                <a href="#" className="text-gray-400 hover:text-green-400 text-sm transition-colors">Cookies</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default SimpleEnercy;
