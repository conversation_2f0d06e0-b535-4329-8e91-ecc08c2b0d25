<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>FreeEnergy - Energia Solar Brasil</title>
    <meta name="description" content="Economia de até 95% na conta de luz com energia solar. Financiamento facilitado e instalação gratuita." />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    
    <style>
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #2ECC71 0%, #27AE60 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        font-family: 'Arial', sans-serif;
      }

      .loading-logo {
        font-size: 3rem;
        font-weight: 800;
        color: white;
        margin-bottom: 2rem;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255,255,255,0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="loading-screen" class="loading-screen">
      <div class="loading-logo">⚡ FreeEnergy</div>
      <div class="loading-spinner"></div>
    </div>

    <div id="root"></div>

    <script>
      console.log('🚀 SISTEMA ULTRA ROBUSTO - Iniciando...');

      let loadingHidden = false;

      window.hideLoading = function() {
        if (loadingHidden) return;
        loadingHidden = true;

        console.log('✅ hideLoading executado');
        const loading = document.getElementById('loading-screen');
        if (loading) {
          loading.style.opacity = '0';
          loading.style.transition = 'opacity 0.5s ease';
          setTimeout(() => {
            loading.style.display = 'none';
            console.log('✅ Loading screen removido');
          }, 500);
        }
      };

      // SISTEMA DE FALLBACK MÚLTIPLO
      let attempts = 0;
      const maxAttempts = 5;

      function checkAndHide() {
        attempts++;
        console.log(`🔄 Tentativa ${attempts}/${maxAttempts} de esconder loading`);

        const root = document.getElementById('root');
        const hasContent = root && root.children.length > 0;

        if (hasContent || attempts >= maxAttempts) {
          console.log('✅ Condições atendidas - escondendo loading');
          window.hideLoading();
        } else if (attempts < maxAttempts) {
          setTimeout(checkAndHide, 1000);
        }
      }

      // Iniciar verificação após 1 segundo
      setTimeout(checkAndHide, 1000);

      // Fallback absoluto após 3 segundos
      setTimeout(() => {
        console.log('⚠️ FALLBACK ABSOLUTO - Forçando hide');
        window.hideLoading();
      }, 3000);

      // Error handler global
      window.addEventListener('error', (e) => {
        console.error('❌ Erro detectado:', e.error);
        setTimeout(() => window.hideLoading(), 500);
      });

      console.log('🎯 Sistema de loading ultra robusto configurado');
    </script>

    <script type="module" src="/src/index.tsx"></script>
  </body>
</html>
