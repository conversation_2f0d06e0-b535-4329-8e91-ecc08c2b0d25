// AI Blog Generator - Automação Inteligente
import OpenAI from 'openai';
import fs from 'fs';
import path from 'path';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY // Gratuito: $5 créditos iniciais
});

// Keywords de alto impacto (Pareto 80/20)
const highImpactKeywords = [
  "energia solar preço 2024",
  "instalação energia solar residencial", 
  "como funciona energia solar",
  "vantagens energia solar",
  "financiamento energia solar",
  "energia solar vale a pena",
  "painéis solares tipos",
  "energia solar apartamento",
  "mercado livre energia",
  "economia energia solar"
];

// Templates SEO otimizados
const seoTemplates = {
  title: (keyword) => `${keyword} | Guia Completo 2024 | FreeEnergy`,
  metaDescription: (keyword) => `✅ Tudo sobre ${keyword}. <PERSON><PERSON><PERSON> completo, preços atualizados e dicas de especialistas. Economize até 95% na conta de luz!`,
  slug: (keyword) => keyword.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, '')
};

// Gerador de conteúdo AI
async function generateBlogPost(keyword) {
  const prompt = `
Escreva um artigo completo e otimizado para SEO sobre "${keyword}" com:

ESTRUTURA:
1. Introdução envolvente (150 palavras)
2. 5-7 seções principais com H2
3. Subsecções com H3 quando necessário
4. Conclusão com CTA
5. FAQ (5 perguntas)

REQUISITOS SEO:
- Palavra-chave principal: "${keyword}"
- Densidade: 1-2%
- LSI keywords relacionadas
- Meta description atrativa
- Títulos otimizados (H1, H2, H3)
- Mínimo 1500 palavras
- Tom conversacional e autoridade

FOCO:
- Energia solar no Brasil
- Economia na conta de luz
- Sustentabilidade
- Casos práticos
- Dados atualizados 2024

FORMATO: Markdown com frontmatter YAML
`;

  try {
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo", // Mais barato que GPT-4
      messages: [{ role: "user", content: prompt }],
      max_tokens: 3000,
      temperature: 0.7
    });

    return completion.choices[0].message.content;
  } catch (error) {
    console.error('Erro na geração AI:', error);
    return null;
  }
}

// Otimizador SEO automático
function optimizeSEO(content, keyword) {
  const slug = seoTemplates.slug(keyword);
  const title = seoTemplates.title(keyword);
  const description = seoTemplates.metaDescription(keyword);
  const date = new Date().toISOString().split('T')[0];
  
  const frontmatter = `---
title: "${title}"
description: "${description}"
keywords: "${keyword}, energia solar, economia energia, sustentabilidade"
author: "FreeEnergy Brasil"
date: "${date}"
slug: "${slug}"
category: "Energia Solar"
tags: ["energia solar", "economia", "sustentabilidade", "brasil"]
image: "/blog/images/${slug}.jpg"
canonical: "https://free-energy-5752f.web.app/blog/${slug}"
---

`;

  return frontmatter + content;
}

// Gerador de imagens automático (Unsplash API gratuita)
async function generateImage(keyword) {
  const unsplashUrl = `https://source.unsplash.com/1200x630/?solar-energy,${encodeURIComponent(keyword)}`;
  return unsplashUrl;
}

// Função principal
async function generateDailyContent() {
  console.log('🚀 Iniciando geração automática de conteúdo...');
  
  // Seleciona keyword aleatória (rotação automática)
  const randomKeyword = highImpactKeywords[Math.floor(Math.random() * highImpactKeywords.length)];
  
  console.log(`📝 Gerando conteúdo para: ${randomKeyword}`);
  
  // Gera conteúdo AI
  const content = await generateBlogPost(randomKeyword);
  
  if (!content) {
    console.error('❌ Falha na geração de conteúdo');
    return;
  }
  
  // Otimiza SEO
  const optimizedContent = optimizeSEO(content, randomKeyword);
  
  // Salva arquivo
  const slug = seoTemplates.slug(randomKeyword);
  const filePath = path.join('client/public/blog', `${slug}.md`);
  
  // Cria diretório se não existir
  const blogDir = path.dirname(filePath);
  if (!fs.existsSync(blogDir)) {
    fs.mkdirSync(blogDir, { recursive: true });
  }
  
  fs.writeFileSync(filePath, optimizedContent);
  
  console.log(`✅ Artigo gerado: ${filePath}`);
  console.log(`🔗 URL: /blog/${slug}`);
  
  // Atualiza sitemap automaticamente
  updateSitemap(slug);
}

// Atualização automática do sitemap
function updateSitemap(slug) {
  const sitemapPath = 'client/public/sitemap.xml';
  const newUrl = `
  <url>
    <loc>https://free-energy-5752f.web.app/blog/${slug}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
    <mobile:mobile/>
  </url>`;
  
  if (fs.existsSync(sitemapPath)) {
    let sitemap = fs.readFileSync(sitemapPath, 'utf8');
    sitemap = sitemap.replace('</urlset>', newUrl + '\n</urlset>');
    fs.writeFileSync(sitemapPath, sitemap);
    console.log('✅ Sitemap atualizado');
  }
}

// Execução
if (process.env.NODE_ENV !== 'test') {
  generateDailyContent().catch(console.error);
}

export { generateDailyContent, generateBlogPost, optimizeSEO };
