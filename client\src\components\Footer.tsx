import { Send, Instagram, Linkedin, Facebook, ArrowRight } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-neutral-900 text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div className="text-2xl font-bold font-montserrat mb-4">
              <span className="text-[#2ECC71]">Free</span><span className="text-[#FFC107]">Energy</span>
            </div>
            <p className="text-gray-400 mb-4">
              Conectando você às melhores soluções de economia energética do Brasil.
            </p>
            <div className="flex space-x-4">
              <a
                href="https://www.instagram.com/freeenergybr/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-[#E4405F] transition-colors"
                aria-label="Instagram Free Energy"
              >
                <Instagram className="h-5 w-5" />
              </a>
              <a
                href="https://www.facebook.com/people/Free-Energy-Economize-Com-Energia-Limpa/61575075471555/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-[#1877F2] transition-colors"
                aria-label="Facebook Free Energy"
              >
                <Facebook className="h-5 w-5" />
              </a>
              <a
                href="https://wa.me/5598981735618?text=Olá! Gostaria de saber mais sobre as soluções de energia da Free Energy."
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-[#25D366] transition-colors"
                aria-label="WhatsApp Free Energy"
              >
                <Send className="h-5 w-5" />
              </a>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-4">Soluções</h3>
            <ul className="space-y-2">
              <li><a href="#solucoes" className="text-gray-400 hover:text-white transition-colors">Para Residências</a></li>
              <li><a href="#solucoes" className="text-gray-400 hover:text-white transition-colors">Para Comércios</a></li>
              <li><a href="#solucoes" className="text-gray-400 hover:text-white transition-colors">Para Indústrias</a></li>
              <li><a href="#solucoes" className="text-gray-400 hover:text-white transition-colors">Energia Solar</a></li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-4">Empresa</h3>
            <ul className="space-y-2">
              <li><a href="#about" className="text-gray-400 hover:text-white transition-colors">Sobre Nós</a></li>
              <li><a href="#cases" className="text-gray-400 hover:text-white transition-colors">Cases de Sucesso</a></li>
              <li><a href="#diferenciais" className="text-gray-400 hover:text-white transition-colors">Parceiros</a></li>
              <li><a href="#contact" className="text-gray-400 hover:text-white transition-colors">Contato</a></li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-4">Fale Conosco</h3>
            <div className="space-y-3 mb-4">
              <p className="text-gray-400 flex items-center gap-2">
                <Send className="h-4 w-4" />
                <a href="mailto:<EMAIL>" className="hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </p>
              <p className="text-gray-400 flex items-center gap-2">
                <Send className="h-4 w-4" />
                <a href="https://wa.me/5598981735618" target="_blank" rel="noopener noreferrer" className="hover:text-white transition-colors">
                  +55 (98) 98173-5618
                </a>
              </p>
            </div>
            <div>
              <a
                href="https://wa.me/5598981735618?text=Olá! Gostaria de saber mais sobre as soluções de energia da Free Energy."
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 bg-[#25D366] hover:bg-green-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-300"
              >
                WhatsApp <ArrowRight className="h-4 w-4" />
              </a>
            </div>
          </div>
        </div>
        
        <div className="mt-12 pt-6 border-t border-gray-800 text-center text-gray-500">
          <div className="mb-4">
            <a href="#" className="mx-3 hover:text-gray-300 transition-colors">Termos de Uso</a>
            <a href="#" className="mx-3 hover:text-gray-300 transition-colors">Política de Privacidade</a>
            <a href="#" className="mx-3 hover:text-gray-300 transition-colors">FAQ</a>
          </div>
          <p>&copy; {new Date().getFullYear()} Free Energy. Todos os direitos reservados.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;