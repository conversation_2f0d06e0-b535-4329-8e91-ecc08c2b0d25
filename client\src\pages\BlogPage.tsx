import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Calendar, Clock, User, ArrowRight, Search, Tag } from 'lucide-react';

interface BlogPost {
  title: string;
  description: string;
  slug: string;
  date: string;
  author: string;
  category: string;
  tags: string[];
  image: string;
  readTime: number;
  featured?: boolean;
  viral?: boolean;
  seoKeywords?: string[];
  metaTitle?: string;
  metaDescription?: string;
  schema?: string;
  content?: string;
}

const BlogPage = () => {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [loading, setLoading] = useState(true);

  // SEO otimizado
  useEffect(() => {
    document.title = "🔥 Blog FreeEnergy - Artigos Virais sobre Energia Solar | Economia de até 95%";

    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content',
        'EXCLUSIVO: Artigos virais sobre energia solar, economia na conta de luz, renda extra e oportunidades imperdíveis. +50.000 leitores economizando até 95%!'
      );
    }

    // Schema.org para SEO
    const schema = {
      "@context": "https://schema.org",
      "@type": "Blog",
      "name": "Blog FreeEnergy",
      "description": "Artigos sobre energia solar, economia e sustentabilidade",
      "url": window.location.href,
      "publisher": {
        "@type": "Organization",
        "name": "FreeEnergy Brasil",
        "logo": {
          "@type": "ImageObject",
          "url": `${window.location.origin}/logo.png`
        }
      }
    };

    const scriptTag = document.createElement('script');
    scriptTag.type = 'application/ld+json';
    scriptTag.textContent = JSON.stringify(schema);
    document.head.appendChild(scriptTag);

    return () => {
      document.head.removeChild(scriptTag);
    };
  }, []);

  // Carregar posts do blog (automático)
  useEffect(() => {
    loadBlogPosts();

    // Listener para atualização automática quando novos posts são gerados
    const handleBlogUpdate = (event: CustomEvent) => {
      console.log('🔄 Evento de atualização recebido, recarregando posts...');
      loadBlogPosts();
    };

    // Adiciona listener
    window.addEventListener('blogPostsUpdated', handleBlogUpdate as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('blogPostsUpdated', handleBlogUpdate as EventListener);
    };
  }, []);

  // Filtrar posts
  useEffect(() => {
    let filtered = posts;
    
    if (searchTerm) {
      filtered = filtered.filter(post => 
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }
    
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(post => post.category === selectedCategory);
    }
    
    setFilteredPosts(filtered);
  }, [posts, searchTerm, selectedCategory]);

  const loadBlogPosts = async () => {
    console.log('🔥 BLOGPAGE - Iniciando carregamento de posts...');

    try {
      // 1. Carregar posts gerados automaticamente (localStorage)
      let generatedPosts: BlogPost[] = [];
      try {
        console.log('🤖 Carregando posts gerados automaticamente...');
        const storedPosts = localStorage.getItem('freeenergy_generated_posts');
        if (storedPosts) {
          const parsedPosts = JSON.parse(storedPosts);
          generatedPosts = Array.isArray(parsedPosts) ? parsedPosts : [];
          console.log(`✅ ${generatedPosts.length} posts gerados carregados`);
        }
      } catch (generatedError) {
        console.warn('⚠️ Erro ao carregar posts gerados:', generatedError);
      }

      // 2. Carregar posts virais estáticos
      let viralPosts: BlogPost[] = [];
      try {
        console.log('📡 Tentando carregar posts virais...');
        const viralResponse = await fetch('/blog/viral-posts.json');
        console.log('📡 Resposta viral-posts:', viralResponse.status, viralResponse.ok);

        if (viralResponse.ok) {
          const viralData = await viralResponse.json();
          viralPosts = Array.isArray(viralData) ? viralData : [];
          console.log(`✅ ${viralPosts.length} posts virais carregados`);
        } else {
          console.warn('⚠️ Resposta não OK para posts virais:', viralResponse.status);
        }
      } catch (viralError) {
        console.warn('⚠️ Erro ao carregar posts virais:', viralError);
      }

      // 3. Carregar posts normais estáticos
      let normalPosts: BlogPost[] = [];
      try {
        console.log('📡 Tentando carregar posts normais...');
        const normalResponse = await fetch('/blog/posts.json');
        console.log('📡 Resposta posts normais:', normalResponse.status, normalResponse.ok);

        if (normalResponse.ok) {
          const normalData = await normalResponse.json();
          normalPosts = Array.isArray(normalData) ? normalData : [];
          console.log(`✅ ${normalPosts.length} posts normais carregados`);
        } else {
          console.error('❌ Resposta não OK para posts normais:', normalResponse.status);
        }
      } catch (normalError) {
        console.error('❌ Erro ao carregar posts normais:', normalError);
      }

      // 4. Combinar todos os posts (gerados primeiro)
      const allPosts = [...generatedPosts, ...viralPosts, ...normalPosts];

      if (allPosts.length > 0) {
        // Ordenar por data (mais recentes primeiro)
        const sortedPosts = allPosts.sort((a, b) => {
          try {
            return new Date(b.date).getTime() - new Date(a.date).getTime();
          } catch (sortError) {
            console.warn('⚠️ Erro ao ordenar posts:', sortError);
            return 0;
          }
        });

        console.log(`✅ Total: ${sortedPosts.length} posts carregados (${generatedPosts.length} gerados + ${viralPosts.length} virais + ${normalPosts.length} normais)`);
        setPosts(sortedPosts);
        setFilteredPosts(sortedPosts);
      } else {
        console.warn('⚠️ Nenhum post carregado, usando fallback');
        throw new Error('Nenhum post carregado');
      }

    } catch (error) {
      console.error('❌ Erro crítico ao carregar posts:', error);

      // Fallback de emergência: posts estáticos
      const fallbackPosts: BlogPost[] = [
        {
          title: "🔥 URGENTE: Governo Anuncia Fim dos Descontos na Conta de Luz",
          description: "EXCLUSIVO: Vazou documento interno que revela mudanças drásticas nas tarifas de energia. Veja como se proteger AGORA.",
          slug: "governo-fim-descontos-conta-luz",
          date: "2024-12-20",
          author: "FreeEnergy",
          category: "URGENTE",
          tags: ["urgente", "governo", "conta de luz"],
          image: "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=800&h=400&fit=crop&q=80",
          readTime: 3,
          viral: true,
          featured: true
        },
        {
          title: "💰 Aposentado Ganha R$ 15.000/Mês com Energia Solar",
          description: "CASO REAL: João, 67 anos, descobriu como transformar energia solar em renda passiva. Em 8 meses já faturou R$ 120.000.",
          slug: "aposentado-ganha-15000-energia-solar",
          date: "2024-12-19",
          author: "FreeEnergy",
          category: "Renda Extra",
          tags: ["renda extra", "energia solar", "aposentado"],
          image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop&q=80",
          readTime: 7,
          viral: true,
          featured: true
        }
      ];

      console.log(`🆘 Usando fallback com ${fallbackPosts.length} posts`);
      setPosts(fallbackPosts);
      setFilteredPosts(fallbackPosts);
    } finally {
      console.log('🏁 Finalizando carregamento de posts');
      setLoading(false);
    }
  };

  const categories = ['all', 'URGENTE', 'Renda Extra', 'Oportunidade', 'Energia Solar', 'Tecnologia', 'Economia', 'Instalação'];

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando artigos...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Header do Blog */}
      <section className="bg-gradient-to-r from-green-600 to-blue-600 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <motion.h1 
            className="text-4xl md:text-6xl font-bold mb-6"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            Blog FreeEnergy
          </motion.h1>
          <motion.p 
            className="text-xl md:text-2xl mb-8 opacity-90"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Tudo sobre energia solar, sustentabilidade e economia
          </motion.p>
          
          {/* Barra de Pesquisa */}
          <motion.div 
            className="max-w-2xl mx-auto relative"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Pesquisar artigos..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-4 rounded-full text-gray-800 text-lg focus:outline-none focus:ring-4 focus:ring-white/30"
            />
          </motion.div>
        </div>
      </section>

      {/* Filtros de Categoria */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-6 py-2 rounded-full transition-all duration-300 ${
                  selectedCategory === category
                    ? 'bg-green-600 text-white shadow-lg'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {category === 'all' ? 'Todos' : category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Grid de Posts */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {filteredPosts.length === 0 ? (
            <div className="text-center py-16">
              <p className="text-gray-600 text-xl">Nenhum artigo encontrado.</p>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredPosts.map((post, index) => (
                <motion.article
                  key={post.slug}
                  className={`bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 group ${
                    post.viral ? 'ring-2 ring-red-500 ring-opacity-50' : ''
                  }`}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  {/* Imagem */}
                  <div className="relative overflow-hidden">
                    <img
                      src={post.image}
                      alt={post.title}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-4 left-4 flex gap-2">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        post.category === 'URGENTE' ? 'bg-red-600 text-white animate-pulse' :
                        post.category === 'Renda Extra' ? 'bg-yellow-600 text-white' :
                        post.category === 'Oportunidade' ? 'bg-purple-600 text-white' :
                        'bg-green-600 text-white'
                      }`}>
                        {post.category}
                      </span>
                      {post.viral && (
                        <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold animate-bounce">
                          🔥 VIRAL
                        </span>
                      )}
                      {post.featured && (
                        <span className="bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                          ⭐ DESTAQUE
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Conteúdo */}
                  <div className="p-6">
                    <h2 className="text-xl font-bold text-gray-800 mb-3 line-clamp-2 group-hover:text-green-600 transition-colors">
                      {post.title}
                    </h2>
                    
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {post.description}
                    </p>

                    {/* Meta Info */}
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          <span>{new Date(post.date).toLocaleDateString('pt-BR')}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          <span>{post.readTime} min</span>
                        </div>
                      </div>
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {post.tags.slice(0, 3).map((tag) => (
                        <span
                          key={tag}
                          className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>

                    {/* CTA */}
                    <button className="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2 group">
                      Ler Artigo
                      <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                    </button>
                  </div>
                </motion.article>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Newsletter CTA */}
      <section className="bg-gradient-to-r from-green-600 to-blue-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Receba Conteúdo Exclusivo
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Artigos semanais sobre energia solar e sustentabilidade
          </p>
          <div className="max-w-md mx-auto flex gap-4">
            <input
              type="email"
              placeholder="Seu melhor e-mail"
              className="flex-1 px-4 py-3 rounded-lg text-gray-800 focus:outline-none focus:ring-4 focus:ring-white/30"
            />
            <button className="bg-white text-green-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
              Inscrever
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default BlogPage;
