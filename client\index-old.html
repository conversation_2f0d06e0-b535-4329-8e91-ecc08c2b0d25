<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">

    <!-- SEO Meta Tags -->
    <title>🔋 Energia Solar e Economia de Energia | FreeEnergy Brasil - Reduza 95% da Conta de Luz</title>
    <meta name="description" content="💡 Transforme sua casa em uma usina de energia limpa! Economia de até 95% na conta de luz com energia solar. Financiamento facilitado e instalação gratuita. Solicite seu orçamento agora!" />
    <meta name="keywords" content="energia solar, economia energia, conta luz, energia limpa, sustentabilidade, financiamento solar, instalação gratuita" />
    <meta name="author" content="FreeEnergy Brasil" />
    <meta name="robots" content="index, follow" />
    <link rel="canonical" href="https://free-energy-5752f.web.app" />

    <!-- Open Graph -->
    <meta property="og:title" content="🔋 FreeEnergy Brasil - Energia Solar que Transforma" />
    <meta property="og:description" content="💡 Reduza até 95% da sua conta de luz com energia solar. Financiamento facilitado e instalação gratuita!" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://free-energy-5752f.web.app" />
    <meta property="og:site_name" content="FreeEnergy Brasil" />

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="🔋 FreeEnergy Brasil - Energia Solar" />
    <meta name="twitter:description" content="💡 Reduza até 95% da conta de luz com energia solar!" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />

    <!-- Loading Styles -->
    <style>
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #2ECC71 0%, #27AE60 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        font-family: 'Montserrat', sans-serif;
      }

      .loading-logo {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 2rem;
        color: white;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255,255,255,0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
      }

      .loading-text {
        color: white;
        font-size: 1.1rem;
        font-weight: 500;
        opacity: 0.9;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .error-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f8f9fa;
        display: none;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9998;
        font-family: 'Montserrat', sans-serif;
        padding: 2rem;
        text-align: center;
      }

      .error-icon {
        font-size: 4rem;
        color: #e74c3c;
        margin-bottom: 1rem;
      }

      .error-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 1rem;
      }

      .error-message {
        color: #7f8c8d;
        margin-bottom: 2rem;
        max-width: 500px;
        line-height: 1.6;
      }

      .error-button {
        background: #3498db;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 6px;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: background 0.3s;
      }

      .error-button:hover {
        background: #2980b9;
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
      <div class="loading-logo">
        <span style="color: #FFC107;">Free</span><span style="color: white;">Energy</span>
      </div>
      <div class="loading-spinner"></div>
      <div class="loading-text">Carregando energia limpa...</div>
    </div>

    <!-- Error Screen -->
    <div id="error-screen" class="error-screen">
      <div class="error-icon">⚠️</div>
      <div class="error-title">Ops! Algo deu errado</div>
      <div class="error-message">
        Não foi possível carregar a aplicação. Verifique sua conexão com a internet e tente novamente.
      </div>
      <button class="error-button" onclick="window.location.reload()">
        🔄 Tentar Novamente
      </button>
    </div>

    <!-- App Root -->
    <div id="root"></div>

    <!-- Error Handling Script -->
    <script>
      // Timeout para esconder loading screen
      setTimeout(() => {
        const loading = document.getElementById('loading-screen');
        const error = document.getElementById('error-screen');
        const root = document.getElementById('root');

        // Se o root ainda estiver vazio após 10 segundos, mostrar erro
        if (!root.hasChildNodes()) {
          loading.style.display = 'none';
          error.style.display = 'flex';
        }
      }, 10000);

      // Capturar erros JavaScript
      window.addEventListener('error', (e) => {
        console.error('Erro capturado:', e.error);
        const loading = document.getElementById('loading-screen');
        const error = document.getElementById('error-screen');
        loading.style.display = 'none';
        error.style.display = 'flex';
      });

      // Capturar erros de Promise rejeitadas
      window.addEventListener('unhandledrejection', (e) => {
        console.error('Promise rejeitada:', e.reason);
        const loading = document.getElementById('loading-screen');
        const error = document.getElementById('error-screen');
        loading.style.display = 'none';
        error.style.display = 'flex';
      });

      // Função para esconder loading quando app carregar
      window.hideLoading = () => {
        const loading = document.getElementById('loading-screen');
        if (loading) {
          loading.style.opacity = '0';
          loading.style.transition = 'opacity 0.5s';
          setTimeout(() => {
            loading.style.display = 'none';
          }, 500);
        }
      };
    </script>

    <script type="module" src="/src/index.tsx"></script>
  </body>
</html>
