import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';

const PreLaunchSection = () => {
  return (
    <section className="py-20 bg-[#2ECC71] bg-opacity-10">
      <div className="container mx-auto px-4">
        <motion.div 
          className="max-w-4xl mx-auto text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <div className="bg-white p-8 rounded-xl shadow-lg relative overflow-hidden">
            <div className="absolute top-0 right-0">
              <img 
                src="https://images.unsplash.com/photo-1613665813446-82a78c468a1d?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" 
                alt="Painéis solares modernos" 
                className="w-40 h-40 object-cover rounded-bl-xl opacity-70"
              />
            </div>
            
            <h2 className="text-3xl font-bold font-montserrat mb-4 relative z-10">
              Em breve: <span className="text-[#2ECC71]">gere sua própria energia</span> sem pagar pela instalação
            </h2>
            <p className="text-lg text-gray-600 mb-8 relative z-10">
              Seja um dos primeiros a ter acesso à nossa revolucionária solução de placas solares gratuitas.
              Converse com um de nossos especialistas para ficar por dentro de todas as novidades.
            </p>
            
            <div className="flex justify-center">
              <a 
                href="https://www.xforcepromotora.com.br/assinatura-energia?affilliate_id=YOCNIH"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 bg-[#FFC107] hover:bg-orange-500 text-white font-bold py-3 px-8 rounded-lg shadow-md transform hover:scale-105 transition-all duration-300"
              >
                Quero saber mais <ArrowRight className="h-5 w-5" />
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default PreLaunchSection;