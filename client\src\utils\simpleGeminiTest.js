/**
 * 🧪 TESTE SIMPLES DA GEMINI API - VERIFICAÇÃO DE CONECTIVIDADE
 */

const simpleGeminiTest = async () => {
  const apiKey = 'AIzaSyDkXmv8NDUJSspRXptExlvS-yaV9J_0yBE';
  
  // Vamos testar diferentes URLs para encontrar a correta
  const urlsToTest = [
    `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`,
    `https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent?key=${apiKey}`,
    `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
    `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${apiKey}`
  ];

  const simplePrompt = {
    contents: [{
      parts: [{
        text: "Responda apenas: OK"
      }]
    }]
  };

  for (let i = 0; i < urlsToTest.length; i++) {
    const url = urlsToTest[i];
    const modelName = url.match(/models\/([^:]+)/)[1];
    
    console.log(`🧪 Testando URL ${i + 1}: ${modelName}`);
    
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(simplePrompt)
      });

      console.log(`📊 Status: ${response.status} ${response.statusText}`);

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ SUCESSO com ${modelName}:`, data);
        
        if (data.candidates && data.candidates[0] && data.candidates[0].content) {
          const content = data.candidates[0].content.parts[0].text;
          console.log(`📝 Resposta: ${content}`);
          return { 
            success: true, 
            url, 
            modelName, 
            response: content,
            fullData: data 
          };
        }
      } else {
        const errorText = await response.text();
        console.log(`❌ Erro ${response.status}: ${errorText}`);
      }
    } catch (error) {
      console.log(`❌ Erro de rede: ${error.message}`);
    }
  }

  return { success: false, error: 'Nenhuma URL funcionou' };
};

// Teste de listagem de modelos
const listGeminiModels = async () => {
  const apiKey = 'AIzaSyDkXmv8NDUJSspRXptExlvS-yaV9J_0yBE';
  const url = `https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`;
  
  console.log('📋 Listando modelos disponíveis...');
  
  try {
    const response = await fetch(url);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Modelos disponíveis:', data);
      return data;
    } else {
      const errorText = await response.text();
      console.log('❌ Erro ao listar modelos:', errorText);
      return null;
    }
  } catch (error) {
    console.log('❌ Erro de rede:', error.message);
    return null;
  }
};

// Exporta para uso global
if (typeof window !== 'undefined') {
  window.simpleGeminiTest = simpleGeminiTest;
  window.listGeminiModels = listGeminiModels;
}

export { simpleGeminiTest, listGeminiModels };
