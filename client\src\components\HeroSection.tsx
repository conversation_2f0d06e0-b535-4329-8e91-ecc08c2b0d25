import { motion } from 'framer-motion';
import { Arrow<PERSON><PERSON>, ZapIcon, SearchCheck, Banknote } from 'lucide-react';

const HeroSection = () => {
  return (
    <section className="py-16 md:py-24 bg-gradient-to-b from-white to-gray-50 overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row items-center">
          <motion.div 
            className="md:w-1/2 md:pr-12 mb-10 md:mb-0"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-4xl md:text-5xl font-bold font-montserrat leading-tight mb-4">
              <span className="block">Economize até</span>
              <span className="text-[#2ECC71]">40% na conta de luz</span>
              <span className="block">sem investimento</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Conectamos você às melhores soluções de energia renovável e economia energética do mercado.
              Tecnologia blockchain, sustentabilidade e sem investimento inicial.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 mb-8">
              <a 
                href="https://www.xforcepromotora.com.br/assinatura-energia?affilliate_id=YOCNIH"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center gap-2 bg-[#2ECC71] hover:bg-green-600 text-white font-bold py-3 px-6 rounded-lg shadow-md transform hover:scale-105 transition-all duration-300"
              >
                Economizar agora <ArrowRight className="h-5 w-5" />
              </a>
              <a 
                href="#simulador" 
                className="inline-flex items-center justify-center gap-2 bg-white border-2 border-[#2ECC71] text-[#2ECC71] hover:bg-[#2ECC71] hover:text-white font-bold py-3 px-6 rounded-lg shadow-sm transition-colors duration-300"
              >
                Simular economia
              </a>
            </div>
            
            <div className="flex flex-wrap gap-6">
              <div className="flex items-center">
                <div className="mr-2 text-[#2ECC71]">
                  <ZapIcon className="h-5 w-5" />
                </div>
                <span className="text-gray-600">Economia garantida</span>
              </div>
              <div className="flex items-center">
                <div className="mr-2 text-[#2ECC71]">
                  <SearchCheck className="h-5 w-5" />
                </div>
                <span className="text-gray-600">Processo transparente</span>
              </div>
              <div className="flex items-center">
                <div className="mr-2 text-[#2ECC71]">
                  <Banknote className="h-5 w-5" />
                </div>
                <span className="text-gray-600">Sem custo inicial</span>
              </div>
            </div>
          </motion.div>
          
          <motion.div 
            className="md:w-1/2 relative"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="relative">
              <div className="absolute -top-10 -left-10 w-40 h-40 bg-[#FFC107] rounded-full opacity-20 blur-2xl"></div>
              <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-[#2ECC71] rounded-full opacity-20 blur-2xl"></div>
              
              <img 
                src="https://images.unsplash.com/photo-**********-6f6b3c19d7c1?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80"
                alt="Pessoa economizando energia" 
                className="rounded-xl shadow-2xl relative z-10 w-full"
              />
              
              <div className="absolute -bottom-8 -left-8 bg-white p-4 rounded-lg shadow-lg max-w-xs z-20 hidden md:block">
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-12 h-12 bg-[#2ECC71] bg-opacity-10 rounded-full flex items-center justify-center">
                    <ZapIcon className="h-6 w-6 text-[#2ECC71]" />
                  </div>
                  <div>
                    <p className="font-bold">Clientes satisfeitos</p>
                    <p className="text-[#2ECC71] font-bold">+1.200</p>
                  </div>
                </div>
              </div>
              
              <motion.div 
                className="absolute top-5 -right-10 bg-white p-3 rounded-lg shadow-lg z-20 hidden md:block"
                initial={{ x: 20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.6, duration: 0.5 }}
              >
                <div className="flex items-center gap-2">
                  <div className="h-8 w-8 rounded-full flex items-center justify-center bg-[#FFC107]">
                    <Banknote className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Economia média</p>
                    <p className="font-bold">30%</p>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
        
        <motion.div 
          className="mt-20 flex justify-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <div className="bg-white p-6 rounded-xl shadow-lg text-center max-w-4xl mx-auto">
            <p className="text-gray-500 mb-4">Empresas que confiam em nossas soluções</p>
            <div className="flex flex-wrap justify-center items-center gap-8 md:gap-12">
              <img src="https://via.placeholder.com/120x40/f8f9fa/6c757d?text=EMPRESA+1" alt="Logo Empresa 1" className="h-8 opacity-70" />
              <img src="https://via.placeholder.com/120x40/f8f9fa/6c757d?text=EMPRESA+2" alt="Logo Empresa 2" className="h-8 opacity-70" />
              <img src="https://via.placeholder.com/120x40/f8f9fa/6c757d?text=EMPRESA+3" alt="Logo Empresa 3" className="h-8 opacity-70" />
              <img src="https://via.placeholder.com/120x40/f8f9fa/6c757d?text=EMPRESA+4" alt="Logo Empresa 4" className="h-8 opacity-70" />
              <img src="https://via.placeholder.com/120x40/f8f9fa/6c757d?text=EMPRESA+5" alt="Logo Empresa 5" className="h-8 opacity-70" />
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default HeroSection;