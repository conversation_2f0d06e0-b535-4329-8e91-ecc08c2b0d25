@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

/* Estilos customizados para sliders */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

input[type="range"]::-webkit-slider-track {
  background: #374151;
  height: 8px;
  border-radius: 4px;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: linear-gradient(135deg, #10b981, #3b82f6);
  height: 20px;
  width: 20px;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
  transition: all 0.2s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 12px rgba(16, 185, 129, 0.4);
}

input[type="range"]::-moz-range-track {
  background: #374151;
  height: 8px;
  border-radius: 4px;
  border: none;
}

input[type="range"]::-moz-range-thumb {
  background: linear-gradient(135deg, #10b981, #3b82f6);
  height: 20px;
  width: 20px;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
  transition: all 0.2s ease;
}

input[type="range"]::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 12px rgba(16, 185, 129, 0.4);
}

/* ⚡ ANIMAÇÕES ULTRA-OTIMIZADAS PARA PERFORMANCE */

/* Animações Simplificadas */
@keyframes particle-float {
  0%, 100% { transform: translateY(0px); opacity: 0.4; }
  50% { transform: translateY(-15px); opacity: 0.8; }
}

@keyframes energy-pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
}

/* Classes Otimizadas */
.animate-float {
  animation: float 4s ease-in-out infinite;
}

.animate-particle-float {
  animation: particle-float 6s ease-in-out infinite;
}

.animate-energy-pulse {
  animation: energy-pulse 3s ease-in-out infinite;
}

/* Gradiente radial */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

/* Scrollbar otimizada */
::-webkit-scrollbar { width: 6px; }
::-webkit-scrollbar-track { background: #1a1a1a; }
::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #10b981, #3b82f6);
  border-radius: 3px;
}

/* Performance optimizations */
* {
  box-sizing: border-box;
}

/* Disable heavy animations on mobile */
@media (max-width: 768px) {
  .animate-particle-float {
    animation-duration: 8s;
  }

  .backdrop-blur-sm { backdrop-filter: blur(4px); }
  .backdrop-blur-xl { backdrop-filter: blur(8px); }
  .blur-xl { filter: blur(8px); }
  .blur-2xl { filter: blur(12px); }
  .blur-3xl { filter: blur(16px); }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }
}