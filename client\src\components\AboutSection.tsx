import { motion } from 'framer-motion';
import { Users, Target, Award, Heart } from 'lucide-react';

const AboutSection = () => {
  const values = [
    {
      icon: <Target className="h-8 w-8 text-[#2ECC71]" />,
      title: "Nossa Missão",
      description: "Democratizar o acesso à energia limpa e econômica, conectando pessoas e empresas às melhores soluções do mercado energético brasileiro."
    },
    {
      icon: <Heart className="h-8 w-8 text-[#2ECC71]" />,
      title: "Nossos Valores",
      description: "Transparência, sustentabilidade e compromisso com o futuro do planeta. Acreditamos que energia limpa deve ser acessível para todos."
    },
    {
      icon: <Award className="h-8 w-8 text-[#2ECC71]" />,
      title: "Nossa Visão",
      description: "Ser a principal plataforma de conexão entre consumidores e soluções de energia sustentável no Brasil até 2030."
    },
    {
      icon: <Users className="h-8 w-8 text-[#2ECC71]" />,
      title: "Nossa Equipe",
      description: "Profissionais especializados em energia, tecnologia e sustentabilidade, dedicados a encontrar a melhor solução para cada cliente."
    }
  ];

  const stats = [
    { number: "10.000+", label: "Clientes Conectados" },
    { number: "50+", label: "Parceiros Especializados" },
    { number: "R$ 2.5M", label: "Economizados pelos Clientes" },
    { number: "95%", label: "Satisfação dos Clientes" }
  ];

  return (
    <section id="about" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-4xl font-bold font-montserrat mb-6">
            Quem <span className="text-[#2ECC71]">Somos</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            A Free Energy é uma plataforma inovadora que atua como intermediadora entre consumidores 
            e as melhores soluções de energia do mercado brasileiro. Nossa missão é educar, orientar 
            e conectar pessoas e empresas às opções mais econômicas e sustentáveis disponíveis.
          </p>
        </motion.div>

        {/* Nossa História */}
        <motion.div
          className="mb-16 bg-gray-50 rounded-2xl p-8 md:p-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-bold font-montserrat mb-6 text-gray-800">
                Nossa <span className="text-[#2ECC71]">História</span>
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Fundada em 2023, a Free Energy nasceu da necessidade de simplificar o acesso às 
                soluções de energia no Brasil. Percebemos que muitas pessoas e empresas não 
                conheciam as opções disponíveis para reduzir seus custos energéticos.
              </p>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Criamos uma plataforma que educa sobre as diferentes modalidades de energia, 
                desde migração para o mercado livre até energia solar, conectando nossos 
                clientes aos parceiros mais adequados para cada perfil de consumo.
              </p>
              <p className="text-gray-600 leading-relaxed">
                <strong>Importante:</strong> A Free Energy atua exclusivamente como intermediadora 
                educacional. Não vendemos energia diretamente, mas conectamos você às melhores 
                soluções do mercado através de nossos parceiros especializados.
              </p>
            </div>
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1497435334941-8c899ee9e8e9?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" 
                alt="Equipe Free Energy" 
                className="rounded-xl shadow-lg w-full"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-[#2ECC71]/20 to-transparent rounded-xl"></div>
            </div>
          </div>
        </motion.div>

        {/* Valores */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                className="text-center p-6 rounded-xl bg-white shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-[#2ECC71]/10 rounded-full">
                    {value.icon}
                  </div>
                </div>
                <h4 className="text-xl font-bold font-montserrat mb-3 text-gray-800">
                  {value.title}
                </h4>
                <p className="text-gray-600 leading-relaxed">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Estatísticas */}
        <motion.div
          className="bg-gradient-to-r from-[#2ECC71] to-[#27AE60] rounded-2xl p-8 md:p-12 text-white"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold font-montserrat mb-4">
              Nossos Resultados
            </h3>
            <p className="text-green-100 text-lg">
              Números que comprovam nosso compromisso com a excelência
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                className="text-center"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <div className="text-4xl md:text-5xl font-bold mb-2">
                  {stat.number}
                </div>
                <div className="text-green-100 font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <h3 className="text-2xl font-bold font-montserrat mb-4 text-gray-800">
            Pronto para economizar na sua conta de energia?
          </h3>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Deixe nossos especialistas encontrarem a melhor solução para o seu perfil de consumo.
          </p>
          <a
            href="#contact"
            className="inline-flex items-center gap-2 bg-[#2ECC71] hover:bg-green-600 text-white font-bold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105"
          >
            Falar com Especialista
          </a>
        </motion.div>
      </div>
    </section>
  );
};

export default AboutSection;
