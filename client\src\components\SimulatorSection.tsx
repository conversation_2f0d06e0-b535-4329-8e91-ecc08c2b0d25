import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, Check, UsersRound, Building, Factory, Zap, HelpCircle } from 'lucide-react';
import { Slider } from "@/components/ui/slider";

const SimulatorSection = () => {
  const [consumptionValue, setConsumptionValue] = useState(1000);
  const [sliderValue, setSliderValue] = useState([1000]); // Slider aceita um array de valores
  const [savingsAmount, setSavingsAmount] = useState(0);
  const [savingsPercentage, setSavingsPercentage] = useState(0);
  const [selectedType, setSelectedType] = useState("residential");
  const [clientGroup, setClientGroup] = useState("B"); // Grupo B (residencial/pequenos) ou Grupo A (industrial)
  const [minValue, setMinValue] = useState(200); // Valor mínimo do simulador
  const [maxValue, setMaxValue] = useState(10000); // Valor máximo inicial

  // Determina o grupo do cliente (A ou B) com base no tipo selecionado
  useEffect(() => {
    // Clientes do Grupo A (industrial/grande porte) ou Grupo B (residencial/pequeno comércio)
    if (selectedType === "industrial") {
      setClientGroup("A");
      setMaxValue(1000000); // Para clientes industriais, permitimos valores muito maiores
      
      // Se o valor atual for muito baixo para um cliente industrial, ajustamos para um valor mais realista
      if (consumptionValue < 5000) {
        setConsumptionValue(10000);
        setSliderValue([10000]);
      }
    } else {
      setClientGroup("B");
      setMaxValue(10000); // Para residenciais e pequenos comércios, limite em 10 mil
      
      // Se vier de um valor muito alto de cliente industrial, ajustamos para um valor mais realista
      if (consumptionValue > 10000) {
        setConsumptionValue(2000);
        setSliderValue([2000]);
      }
    }
  }, [selectedType]);

  // Calcula a economia baseada no tipo de cliente (grupo) e valor do consumo
  useEffect(() => {
    const value = consumptionValue;
    let percentage = 0;
    
    // Determina a porcentagem baseado no grupo do cliente e valor da conta
    if (clientGroup === "B") {
      // Grupo B: economia escalonada por faixa de valor (XForce Promotora)
      if (value < 500) {
        percentage = 12; // Economia menor para contas pequenas
      } else if (value < 1000) {
        percentage = 15; // Economia média para contas médias
      } else if (value < 3000) {
        percentage = 18; // Economia maior para contas altas
      } else {
        percentage = 20; // Economia máxima para contas muito altas
      }
    } else {
      // Grupo A: economia escalonada por faixa de valor (Lead Energy)
      if (value < 10000) {
        percentage = 25; // Economia média para empresas menores
      } else if (value < 50000) {
        percentage = 30; // Economia considerável para médias empresas
      } else if (value < 200000) {
        percentage = 35; // Economia alta para grandes empresas
      } else {
        percentage = 40; // Economia máxima para empresas muito grandes
      }
    }
    
    setSavingsPercentage(percentage);
    setSavingsAmount(Math.round((value * percentage / 100) * 12)); // Economia anual
  }, [consumptionValue, clientGroup]);

  // Atualiza o valor do consumo quando o slider muda
  const handleSliderChange = (value: number[]) => {
    setSliderValue(value);
    setConsumptionValue(value[0]);
  };
  
  // Função para formatar números grandes com separadores para melhor visualização
  const formatCurrency = (value: number): string => {
    return value.toLocaleString('pt-BR');
  };

  // Tipos de consumidores e seus detalhes
  const consumerTypes = [
    {
      id: "residential",
      title: "Residencial",
      description: "Para casas e apartamentos (Grupo B)",
      icon: <UsersRound className="h-8 w-8 text-white" />,
      color: "#2ECC71",
      group: "B"
    },
    {
      id: "small_commercial",
      title: "Pequeno Comércio",
      description: "Lojas e pequenas empresas (Grupo B)",
      icon: <Building className="h-8 w-8 text-white" />,
      color: "#FFC107",
      group: "B"
    },
    {
      id: "industrial",
      title: "Grande Consumidor",
      description: "Indústrias e empresas de grande porte (Grupo A)",
      icon: <Factory className="h-8 w-8 text-white" />,
      color: "#2196F3",
      group: "A"
    }
  ];

  return (
    <section id="simulador" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl font-bold font-montserrat mb-4 text-center">
            Quanto você pode <span className="text-[#2ECC71]">economizar?</span>
          </h2>
          <p className="text-lg text-gray-600 text-center mb-6">
            Simule sua migração para uma solução mais econômica - sem investimento inicial
          </p>
          <div className="flex flex-col md:flex-row items-center justify-center gap-4 py-3 px-4 bg-blue-50 rounded-lg mb-8 max-w-3xl mx-auto">
            <Zap className="h-8 w-8 text-blue-500 flex-shrink-0" />
            <p className="text-sm text-gray-700 text-center md:text-left">
              <strong>Como funciona:</strong> Selecionamos as melhores soluções parceiras para seu perfil. 
              Cada tipo de consumidor (Grupo A ou B) é direcionado para parceiros específicos com ofertas personalizadas.
              Apenas selecione seu tipo e simule sua economia.
            </p>
          </div>
          <p className="text-md text-gray-600 text-center mb-10 max-w-2xl mx-auto">
            Na Free Energy, somos intermediadores entre suas necessidades e as melhores soluções parceiras do mercado. 
            Facilitamos sua migração para opções mais econômicas sem nenhum investimento inicial.
          </p>
        </motion.div>
        
        {/* Seleção de tipo de consumidor */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-10">
          {consumerTypes.map((type) => (
            <motion.div 
              key={type.id}
              className={`cursor-pointer p-4 rounded-lg border-2 transition-all duration-200 ${
                selectedType === type.id 
                  ? `border-${type.color.replace('#', '')} bg-${type.color.replace('#', '')}/10` 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedType(type.id)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center">
                <div className="p-3 rounded-full mr-4" style={{backgroundColor: type.color}}>
                  {type.icon}
                </div>
                <div>
                  <h3 className="text-xl font-bold">{type.title}</h3>
                  <p className="text-sm text-gray-500">{type.description}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
        
        {/* Simulador com slider */}
        <motion.div 
          className="max-w-3xl mx-auto bg-gray-50 p-8 rounded-xl shadow-lg mb-10"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-2">Valor médio da sua conta mensal (R$):</h3>
            <div className="mb-2">
              <Slider 
                value={sliderValue}
                onValueChange={handleSliderChange}
                min={minValue}
                max={maxValue} 
                step={clientGroup === "A" ? 1000 : 100}
                className="my-6"
              />
              <div className="flex justify-between text-sm text-gray-500 mt-1">
                <span>R$ {formatCurrency(minValue)}</span>
                <span>R$ {formatCurrency(clientGroup === "A" ? maxValue/2 : maxValue/2)}</span>
                <span>R$ {formatCurrency(maxValue)}</span>
              </div>
            </div>
            {clientGroup === "A" && (
              <p className="text-xs text-blue-600 italic mb-3 text-center">
                Para grandes consumidores (Grupo A), o simulador suporta valores até R$ 1.000.000
              </p>
            )}
            <div className="flex flex-col justify-center items-center">
              <div className="text-center bg-white px-6 py-3 rounded-lg border border-gray-200 shadow-sm mb-3">
                <span className="text-lg">Valor atual: </span>
                <span className="text-2xl font-bold">R$ {formatCurrency(consumptionValue)}</span>
              </div>
              
              {clientGroup === "A" && (
                <div className="mt-2 w-full max-w-md">
                  <label htmlFor="customValue" className="block text-sm font-medium text-gray-700 mb-1">
                    Ou digite um valor personalizado:
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="number"
                      id="customValue"
                      min={minValue}
                      max={maxValue}
                      placeholder="Ex: 250000"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          const input = e.target as HTMLInputElement;
                          const value = parseInt(input.value);
                          if (!isNaN(value) && value >= minValue && value <= maxValue) {
                            setConsumptionValue(value);
                            setSliderValue([value]);
                          }
                        }
                      }}
                    />
                    <button
                      className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                      onClick={(e) => {
                        const input = document.getElementById('customValue') as HTMLInputElement;
                        const value = parseInt(input.value);
                        if (!isNaN(value) && value >= minValue && value <= maxValue) {
                          setConsumptionValue(value);
                          setSliderValue([value]);
                        }
                      }}
                    >
                      Aplicar
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Para contas de alto valor, digite o montante exato para uma simulação mais precisa.
                  </p>
                </div>
              )}
            </div>
          </div>
          
          {/* Resultados da economia */}
          <div className="bg-[#2ECC71] bg-opacity-10 border border-[#2ECC71] rounded-lg p-6 mb-6">
            <div className="text-center mb-4">
              <p className="text-lg font-medium">Sua economia estimada:</p>
              <h3 className="text-3xl font-bold text-[#2ECC71]">
                R$ {formatCurrency(savingsAmount)} <span className="text-lg font-normal text-gray-600">por ano</span>
              </h3>
              <p className="text-gray-600 mt-1">
                Aproximadamente <span className="font-semibold">{savingsPercentage}%</span> de economia na sua conta 
                {clientGroup === "B" 
                  ? " (até 20% para Grupo B)" 
                  : " (até 40% para Grupo A)"}
              </p>
            </div>
            <div className="mt-4 bg-white p-4 rounded-lg">
              <p className="font-medium mb-2">Como funciona essa economia?</p>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-[#2ECC71] mt-1 mr-2 flex-shrink-0" />
                  <span className="text-sm">Conectamos você diretamente a parceiros com as melhores soluções para seu caso</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-[#2ECC71] mt-1 mr-2 flex-shrink-0" />
                  <span className="text-sm">Migração simples para solução mais econômica sem necessidade de investimento inicial</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-[#2ECC71] mt-1 mr-2 flex-shrink-0" />
                  <span className="text-sm">Necessário enviar sua conta de luz para receber proposta personalizada</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-[#2ECC71] mt-1 mr-2 flex-shrink-0" />
                  <span className="text-sm">Economia imediata na sua fatura de energia</span>
                </li>
              </ul>
            </div>
          </div>
          
          <div className="flex justify-center">
            <a 
              href={selectedType === "residential" || selectedType === "small_commercial" 
                ? "https://www.xforcepromotora.com.br/assinatura-energia?affilliate_id=YOCNIH" 
                : "https://www.leadenergy.com.br/simular?ch=0f0547d3-7e73-442e-a915-34ef39efe74b&pr=a048c6c3-6224-43ab-ad95-7727cf36d2a2"}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 bg-[#FFC107] hover:bg-orange-500 text-white font-bold py-4 px-8 rounded-lg shadow-md transform hover:scale-105 transition-all duration-300"
            >
              Garantir minha economia <ArrowRight className="h-5 w-5" />
            </a>
          </div>
        </motion.div>
        
        <motion.div
          className="mt-8"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <div className="bg-gray-50 p-6 rounded-lg shadow-sm max-w-3xl mx-auto border border-gray-200">
            <div className="flex items-start gap-3">
              <HelpCircle className="h-6 w-6 text-gray-400 flex-shrink-0 mt-1" />
              <div>
                <h4 className="font-bold text-gray-700 mb-2">Informações importantes</h4>
                <p className="text-gray-600 text-sm mb-3">
                  Os valores apresentados são estimativas baseadas em casos reais de economia. 
                  Os resultados podem variar dependendo do seu perfil de consumo, localização e 
                  disponibilidade das soluções parceiras na sua região.
                </p>
                <p className="text-gray-500 text-sm">
                  A Free Energy atua como intermediadora entre clientes e soluções parceiras, educando e indicando 
                  as melhores opções disponíveis no mercado. Não nos responsabilizamos pela implementação final das soluções, 
                  que são fornecidas por nossos parceiros especializados. Buscamos sempre as soluções mais inovadoras 
                  e econômicas para atender às necessidades específicas de cada cliente.
                </p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default SimulatorSection;