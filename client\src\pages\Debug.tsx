import { useLocation } from 'wouter';
import { useEffect, useState } from 'react';

const Debug = () => {
  const [location, setLocation] = useLocation();
  const [debugInfo, setDebugInfo] = useState({
    wouter: location,
    window: '',
    hash: '',
    search: ''
  });

  useEffect(() => {
    setDebugInfo({
      wouter: location,
      window: window.location.pathname,
      hash: window.location.hash,
      search: window.location.search
    });
  }, [location]);

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-8 text-green-400">🔍 Debug Page</h1>
        
        <div className="bg-gray-800 p-6 rounded-lg mb-6">
          <h2 className="text-2xl font-bold mb-4">Routing Information</h2>
          <div className="space-y-2">
            <p><strong>Wouter Location:</strong> {debugInfo.wouter}</p>
            <p><strong>Window Pathname:</strong> {debugInfo.window}</p>
            <p><strong>Hash:</strong> {debugInfo.hash}</p>
            <p><strong>Search:</strong> {debugInfo.search}</p>
            <p><strong>Full URL:</strong> {window.location.href}</p>
          </div>
        </div>

        <div className="bg-gray-800 p-6 rounded-lg mb-6">
          <h2 className="text-2xl font-bold mb-4">Navigation Test</h2>
          <div className="space-x-4">
            <button
              onClick={() => setLocation('/')}
              className="px-4 py-2 bg-blue-600 rounded hover:bg-blue-700"
            >
              Go to Home
            </button>
            <button
              onClick={() => setLocation('/enercy')}
              className="px-4 py-2 bg-green-600 rounded hover:bg-green-700"
            >
              Go to ENERCY
            </button>
            <button
              onClick={() => window.location.href = '/enercy'}
              className="px-4 py-2 bg-purple-600 rounded hover:bg-purple-700"
            >
              Force Navigate to ENERCY
            </button>
          </div>
        </div>

        <div className="bg-gray-800 p-6 rounded-lg">
          <h2 className="text-2xl font-bold mb-4">Environment Info</h2>
          <div className="space-y-2">
            <p><strong>User Agent:</strong> {navigator.userAgent}</p>
            <p><strong>Host:</strong> {window.location.host}</p>
            <p><strong>Protocol:</strong> {window.location.protocol}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Debug;
