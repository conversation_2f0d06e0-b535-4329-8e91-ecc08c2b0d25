import { useLocation } from 'wouter';
import { useEffect, useState } from 'react';

const Debug = () => {
  const [location, setLocation] = useLocation();
  const [debugInfo, setDebugInfo] = useState({
    wouter: location,
    window: '',
    hash: '',
    search: ''
  });
  const [testResults, setTestResults] = useState<any>(null);
  const [isRunningTests, setIsRunningTests] = useState(false);

  useEffect(() => {
    setDebugInfo({
      wouter: location,
      window: window.location.pathname,
      hash: window.location.hash,
      search: window.location.search
    });
  }, [location]);

  const runBlogTests = async () => {
    setIsRunningTests(true);
    try {
      const { default: blogSystemTester } = await import('../utils/blogSystemTester.js');
      const results = await blogSystemTester.runAllTests();
      setTestResults(results);
    } catch (error) {
      console.error('❌ Erro ao executar testes:', error);
      setTestResults([{ test: 'LOAD_ERROR', passed: false, message: error.message }]);
    } finally {
      setIsRunningTests(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-8 text-green-400">🔍 Debug Page</h1>
        
        <div className="bg-gray-800 p-6 rounded-lg mb-6">
          <h2 className="text-2xl font-bold mb-4">Routing Information</h2>
          <div className="space-y-2">
            <p><strong>Wouter Location:</strong> {debugInfo.wouter}</p>
            <p><strong>Window Pathname:</strong> {debugInfo.window}</p>
            <p><strong>Hash:</strong> {debugInfo.hash}</p>
            <p><strong>Search:</strong> {debugInfo.search}</p>
            <p><strong>Full URL:</strong> {window.location.href}</p>
          </div>
        </div>

        <div className="bg-gray-800 p-6 rounded-lg mb-6">
          <h2 className="text-2xl font-bold mb-4">Navigation Test</h2>
          <div className="space-x-4">
            <button
              onClick={() => setLocation('/')}
              className="px-4 py-2 bg-blue-600 rounded hover:bg-blue-700"
            >
              Go to Home
            </button>
            <button
              onClick={() => setLocation('/enercy')}
              className="px-4 py-2 bg-green-600 rounded hover:bg-green-700"
            >
              Go to ENERCY
            </button>
            <button
              onClick={() => window.location.href = '/enercy'}
              className="px-4 py-2 bg-purple-600 rounded hover:bg-purple-700"
            >
              Force Navigate to ENERCY
            </button>
            <button
              onClick={() => setLocation('/blog')}
              className="px-4 py-2 bg-yellow-600 rounded hover:bg-yellow-700"
            >
              Go to Blog
            </button>
          </div>
        </div>

        <div className="bg-gray-800 p-6 rounded-lg mb-6">
          <h2 className="text-2xl font-bold mb-4">🧪 Blog System Tests</h2>
          <div className="mb-4">
            <button
              onClick={runBlogTests}
              disabled={isRunningTests}
              className="px-6 py-3 bg-blue-600 rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isRunningTests ? '🔄 Executando Testes...' : '🚀 Executar Testes do Blog'}
            </button>
          </div>

          {testResults && (
            <div className="mt-4">
              <h3 className="text-xl font-bold mb-2">Resultados dos Testes:</h3>
              <div className="bg-gray-900 p-4 rounded max-h-96 overflow-y-auto">
                {testResults.map((result: any, index: number) => (
                  <div key={index} className={`mb-2 p-2 rounded ${result.passed ? 'bg-green-900' : 'bg-red-900'}`}>
                    <span className="font-mono text-sm">
                      {result.passed ? '✅' : '❌'} {result.test}: {result.message}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="bg-gray-800 p-6 rounded-lg">
          <h2 className="text-2xl font-bold mb-4">Environment Info</h2>
          <div className="space-y-2">
            <p><strong>User Agent:</strong> {navigator.userAgent}</p>
            <p><strong>Host:</strong> {window.location.host}</p>
            <p><strong>Protocol:</strong> {window.location.protocol}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Debug;
