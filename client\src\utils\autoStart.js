/**
 * 🚀 AUTO-START SYSTEM - MÍNIMO ESFORÇO, MÁXIMO RESULTADO
 * Inicia automaticamente o Gemini Agent quando a página carrega
 */

class AutoStartSystem {
  constructor() {
    this.initialized = false;
    this.geminiAgent = null;
  }

  /**
   * 🚀 INICIALIZAÇÃO AUTOMÁTICA
   */
  async initialize() {
    if (this.initialized) return;

    console.log('🚀 Iniciando sistema automático...');
    console.log('📊 Aplicando Lei de Pareto (80/20)');
    
    try {
      // Aguardar carregamento da página
      if (document.readyState !== 'complete') {
        await new Promise(resolve => {
          window.addEventListener('load', resolve);
        });
      }

      // Aguardar um pouco para garantir que tudo carregou
      await this.delay(2000);

      // Carregar Gemini Agent
      await this.loadGeminiAgent();

      // Verificar se deve auto-iniciar
      if (this.shouldAutoStart()) {
        await this.startAutomation();
      }

      this.initialized = true;
      console.log('✅ Sistema automático inicializado!');

    } catch (error) {
      console.error('❌ Erro na inicialização automática:', error);
    }
  }

  /**
   * 🤖 CARREGAR GEMINI AGENT
   */
  async loadGeminiAgent() {
    try {
      // Tentar carregar do window global primeiro
      if (window.geminiAgent) {
        this.geminiAgent = window.geminiAgent;
        console.log('✅ Gemini Agent encontrado no window global');
        return;
      }

      // Carregar dinamicamente
      const module = await import('../services/geminiAgent.js');
      this.geminiAgent = module.default;
      console.log('✅ Gemini Agent carregado dinamicamente');

    } catch (error) {
      console.error('❌ Erro ao carregar Gemini Agent:', error);
      throw error;
    }
  }

  /**
   * 🎯 VERIFICAR SE DEVE AUTO-INICIAR
   */
  shouldAutoStart() {
    // Verificar se está na página do blog
    if (!window.location.pathname.includes('/blog')) {
      return false;
    }

    // Verificar se já tem posts recentes (últimas 2 horas)
    try {
      const posts = JSON.parse(localStorage.getItem('freeenergy_generated_posts') || '[]');
      if (posts.length > 0) {
        const lastPost = posts[0];
        const lastPostTime = new Date(lastPost.createdAt || lastPost.publishedAt);
        const twoHoursAgo = new Date(Date.now() - 2 * 60 * 60 * 1000);
        
        if (lastPostTime > twoHoursAgo) {
          console.log('⏰ Post recente encontrado, aguardando...');
          return false;
        }
      }
    } catch (error) {
      console.log('⚠️ Erro ao verificar posts, continuando...');
    }

    // Verificar horário otimizado (9h-22h)
    const hour = new Date().getHours();
    if (hour < 9 || hour > 22) {
      console.log('🌙 Fora do horário otimizado, aguardando...');
      return false;
    }

    return true;
  }

  /**
   * 🚀 INICIAR AUTOMAÇÃO
   */
  async startAutomation() {
    if (!this.geminiAgent) {
      console.error('❌ Gemini Agent não disponível');
      return;
    }

    try {
      console.log('🤖 Iniciando automação do Gemini Agent...');
      await this.geminiAgent.startFullAutomation();
      
      // Salvar estado
      localStorage.setItem('gemini_agent_auto_started', 'true');
      
      console.log('✅ Automação iniciada com sucesso!');
      
      // Mostrar notificação discreta
      this.showNotification('🤖 Gemini Agent ativado automaticamente!');
      
    } catch (error) {
      console.error('❌ Erro ao iniciar automação:', error);
    }
  }

  /**
   * 📢 MOSTRAR NOTIFICAÇÃO DISCRETA
   */
  showNotification(message) {
    // Criar notificação no canto da tela
    const notification = document.createElement('div');
    notification.innerHTML = `
      <div style="
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        font-weight: 500;
        max-width: 300px;
        animation: slideIn 0.3s ease-out;
      ">
        ${message}
      </div>
      <style>
        @keyframes slideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
      </style>
    `;

    document.body.appendChild(notification);

    // Remover após 5 segundos
    setTimeout(() => {
      notification.style.animation = 'slideIn 0.3s ease-out reverse';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 5000);
  }

  /**
   * ⏱️ DELAY HELPER
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 📊 STATUS DO SISTEMA
   */
  getStatus() {
    return {
      initialized: this.initialized,
      geminiAgentLoaded: !!this.geminiAgent,
      autoStarted: localStorage.getItem('gemini_agent_auto_started') === 'true',
      currentPage: window.location.pathname,
      currentHour: new Date().getHours()
    };
  }
}

// Criar instância global
const autoStartSystem = new AutoStartSystem();

// Inicializar automaticamente
if (typeof window !== 'undefined') {
  // Aguardar DOM estar pronto
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      autoStartSystem.initialize();
    });
  } else {
    // DOM já está pronto
    setTimeout(() => {
      autoStartSystem.initialize();
    }, 1000);
  }
}

// Exportar para uso global
window.autoStartSystem = autoStartSystem;

export default autoStartSystem;
