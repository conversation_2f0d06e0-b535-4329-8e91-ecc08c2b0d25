import { useState } from 'react';
import { <PERSON>u, X, ArrowR<PERSON> } from 'lucide-react';

const Header = () => {
  const [menuOpen, setMenuOpen] = useState(false);

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  return (
    <header className="bg-white shadow-sm fixed w-full z-50">
      <div className="container mx-auto px-4 py-3 flex justify-between items-center">
        <div className="flex items-center">
          <a href="/" className="text-2xl font-bold font-montserrat">
            <span className="text-[#2ECC71]">Free</span><span className="text-[#FFC107]">Energy</span>
          </a>
        </div>
        <nav className="hidden md:flex items-center space-x-6">
          <a href="#como-funciona" className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium">
            Como Funciona
          </a>
          <a href="#about" className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium">
            Quem Somos
          </a>
          <a href="#solucoes" className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium">
            Soluções
          </a>
          <a href="#simulador" className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium">
            Simulador
          </a>
          <a href="#cases" className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium">
            Cases
          </a>
          <a href="/blog" className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium">
            Blog
          </a>
        </nav>
        <a
          href="https://wa.me/5598981735618?text=Olá! Gostaria de saber mais sobre as soluções de energia da Free Energy."
          target="_blank"
          rel="noopener noreferrer"
          className="hidden md:flex items-center gap-2 bg-[#25D366] text-white px-4 py-2 rounded-lg font-semibold hover:bg-opacity-90 transition-all duration-300 shadow-md"
        >
          WhatsApp <ArrowRight className="h-4 w-4" />
        </a>
        <button 
          className="md:hidden text-neutral-900"
          onClick={toggleMenu}
        >
          {menuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </button>
      </div>
      
      {/* Mobile menu */}
      {menuOpen && (
        <div className="md:hidden bg-white w-full absolute shadow-lg">
          <div className="container mx-auto px-4 py-3 flex flex-col space-y-4">
            <a
              href="#como-funciona"
              className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium py-2"
              onClick={() => setMenuOpen(false)}
            >
              Como Funciona
            </a>
            <a
              href="#about"
              className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium py-2"
              onClick={() => setMenuOpen(false)}
            >
              Quem Somos
            </a>
            <a
              href="#solucoes"
              className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium py-2"
              onClick={() => setMenuOpen(false)}
            >
              Soluções
            </a>
            <a 
              href="#simulador" 
              className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium py-2"
              onClick={() => setMenuOpen(false)}
            >
              Simulador
            </a>
            <a 
              href="#cases" 
              className="text-neutral-900 hover:text-[#2ECC71] transition-colors font-medium py-2"
              onClick={() => setMenuOpen(false)}
            >
              Cases
            </a>
            <a
              href="https://wa.me/5598981735618?text=Olá! Gostaria de saber mais sobre as soluções de energia da Free Energy."
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center gap-2 bg-[#25D366] text-white px-4 py-2 rounded-lg font-semibold hover:bg-opacity-90 transition-all duration-300 shadow-md w-full"
              onClick={() => setMenuOpen(false)}
            >
              WhatsApp <ArrowRight className="h-4 w-4" />
            </a>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
