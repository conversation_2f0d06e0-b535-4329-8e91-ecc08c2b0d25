<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Debug React App - FreeEnergy</title>
    
    <style>
      body {
        margin: 0;
        padding: 0;
        background: #000;
        color: #fff;
        font-family: Arial, sans-serif;
      }
      
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #2ECC71 0%, #27AE60 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }

      .loading-logo {
        font-size: 3rem;
        font-weight: 800;
        color: white;
        margin-bottom: 2rem;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255,255,255,0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="loading-screen" class="loading-screen">
      <div class="loading-logo">⚡ Debug React</div>
      <div class="loading-spinner"></div>
    </div>

    <div id="root"></div>

    <script>
      window.hideLoading = function() {
        const loading = document.getElementById('loading-screen');
        if (loading) {
          setTimeout(() => {
            loading.style.display = 'none';
          }, 500);
        }
      };

      console.log('🔧 Debug HTML carregado, aguardando React...');
    </script>

    <script type="module" src="/src/test-index.tsx"></script>
  </body>
</html>
