/**
 * 🤖 HOOK DE AUTOMAÇÃO COMPLETA - FREEENERGY
 * Orquestra todo o processo de geração e publicação automática
 */

import { useState, useEffect, useCallback } from 'react';
import ContentGenerator from '../services/contentGenerator';
import KeywordManager from '../services/keywordManager';
import AutoPublisher from '../services/autoPublisher';
import RealPublisher from '../services/realPublisher';

const useAutoBlogger = () => {
  const [isActive, setIsActive] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [lastGeneration, setLastGeneration] = useState(null);
  const [stats, setStats] = useState({});
  const [logs, setLogs] = useState([]);
  const [config, setConfig] = useState({
    interval: 24 * 60 * 60 * 1000, // 24 horas em ms
    autoStart: true,
    maxDailyPosts: 3,
    enableNotifications: true
  });

  // Instâncias dos serviços
  const contentGenerator = new ContentGenerator();
  const keywordManager = new KeywordManager();
  const autoPublisher = new AutoPublisher();
  const realPublisher = new RealPublisher();

  /**
   * 🚀 PROCESSO PRINCIPAL DE GERAÇÃO
   */
  const generateAndPublishArticle = useCallback(async () => {
    if (isGenerating) {
      addLog('⚠️ Geração já em andamento, pulando...');
      return;
    }

    setIsGenerating(true);
    addLog('🚀 Iniciando geração automática de artigo...');

    try {
      // Verifica limite diário
      if (!canGenerateToday()) {
        addLog('⚠️ Limite diário de posts atingido');
        setIsGenerating(false);
        return;
      }

      // 1. Seleciona keyword
      addLog('🎯 Selecionando keyword...');
      const keywordData = keywordManager.getNextKeyword();
      addLog(`✅ Keyword selecionada: ${keywordData.keyword}`);

      // 2. Gera artigo
      addLog('📝 Gerando artigo com IA...');
      const article = await contentGenerator.generateArticle(keywordData.keyword);
      addLog(`✅ Artigo gerado: ${article.title}`);

      // 3. Publica artigo no blog real
      addLog('📤 Publicando artigo no blog...');
      const publishResult = await realPublisher.publishArticle(article);

      if (publishResult.success) {
        addLog(`🎉 Artigo publicado no blog com sucesso!`);

        // Também registra no sistema antigo para estatísticas
        await autoPublisher.publishArticle(article);

        // Atualiza estatísticas
        updateStats();

        // Registra última geração
        setLastGeneration({
          timestamp: new Date().toISOString(),
          keyword: keywordData.keyword,
          title: article.title,
          slug: publishResult.article.slug,
          success: true
        });

        // Notifica sucesso
        if (config.enableNotifications) {
          showNotification('🚀 Novo artigo publicado no blog!', article.title);
        }

        addLog(`✅ Artigo disponível no blog: /blog`);

      } else {
        throw new Error(publishResult.error);
      }

    } catch (error) {
      addLog(`❌ Erro na geração: ${error.message}`);
      
      setLastGeneration({
        timestamp: new Date().toISOString(),
        error: error.message,
        success: false
      });
    } finally {
      setIsGenerating(false);
    }
  }, [isGenerating, config]);

  /**
   * 📊 VERIFICA LIMITE DIÁRIO
   */
  const canGenerateToday = useCallback(() => {
    const publishedToday = autoPublisher.getPublishingStats().publishedToday;
    return publishedToday < config.maxDailyPosts;
  }, [config.maxDailyPosts]);

  /**
   * 📝 ADICIONA LOG
   */
  const addLog = useCallback((message) => {
    const logEntry = {
      timestamp: new Date().toISOString(),
      message,
      id: Date.now()
    };
    
    setLogs(prev => {
      const newLogs = [logEntry, ...prev];
      // Mantém apenas os últimos 50 logs
      return newLogs.slice(0, 50);
    });
    
    console.log(`[AutoBlogger] ${message}`);
  }, []);

  /**
   * 📊 ATUALIZA ESTATÍSTICAS
   */
  const updateStats = useCallback(() => {
    const keywordStats = keywordManager.getStats();
    const publishingStats = autoPublisher.getPublishingStats();
    
    setStats({
      keywords: keywordStats,
      publishing: publishingStats,
      lastUpdate: new Date().toISOString()
    });
  }, []);

  /**
   * 🔔 MOSTRA NOTIFICAÇÃO
   */
  const showNotification = useCallback((title, message) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(title, {
        body: message,
        icon: '/logo.png',
        badge: '/logo.png'
      });
    }
  }, []);

  /**
   * ⏰ CONFIGURA TIMER AUTOMÁTICO
   */
  useEffect(() => {
    let intervalId;

    if (isActive) {
      addLog(`⏰ Timer ativado: ${config.interval / (60 * 60 * 1000)}h`);
      
      // Executa imediatamente se configurado
      if (config.autoStart && !lastGeneration) {
        setTimeout(generateAndPublishArticle, 5000); // 5s delay
      }

      // Configura execução periódica
      intervalId = setInterval(() => {
        addLog('⏰ Timer disparado - iniciando geração...');
        generateAndPublishArticle();
      }, config.interval);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
        addLog('⏰ Timer desativado');
      }
    };
  }, [isActive, config, generateAndPublishArticle, lastGeneration]);

  /**
   * 🔄 CARREGA DADOS INICIAIS
   */
  useEffect(() => {
    // Carrega configuração salva
    const savedConfig = localStorage.getItem('autoblogger_config');
    if (savedConfig) {
      try {
        const parsed = JSON.parse(savedConfig);
        setConfig(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Erro ao carregar configuração:', error);
      }
    }

    // Carrega última geração
    const savedLastGeneration = localStorage.getItem('autoblogger_last_generation');
    if (savedLastGeneration) {
      try {
        setLastGeneration(JSON.parse(savedLastGeneration));
      } catch (error) {
        console.error('Erro ao carregar última geração:', error);
      }
    }

    // Atualiza estatísticas
    updateStats();

    // Solicita permissão para notificações
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }

    addLog('🚀 AutoBlogger inicializado');
  }, [updateStats]);

  /**
   * 💾 SALVA CONFIGURAÇÃO
   */
  useEffect(() => {
    localStorage.setItem('autoblogger_config', JSON.stringify(config));
  }, [config]);

  /**
   * 💾 SALVA ÚLTIMA GERAÇÃO
   */
  useEffect(() => {
    if (lastGeneration) {
      localStorage.setItem('autoblogger_last_generation', JSON.stringify(lastGeneration));
    }
  }, [lastGeneration]);

  /**
   * 🎮 CONTROLES MANUAIS
   */
  const startAutomation = useCallback(() => {
    setIsActive(true);
    addLog('🟢 Automação iniciada');
  }, [addLog]);

  const stopAutomation = useCallback(() => {
    setIsActive(false);
    addLog('🔴 Automação parada');
  }, [addLog]);

  const generateNow = useCallback(() => {
    addLog('🚀 Geração manual iniciada');
    generateAndPublishArticle();
  }, [generateAndPublishArticle, addLog]);

  const updateConfig = useCallback((newConfig) => {
    setConfig(prev => ({ ...prev, ...newConfig }));
    addLog('⚙️ Configuração atualizada');
  }, [addLog]);

  const clearLogs = useCallback(() => {
    setLogs([]);
    addLog('🧹 Logs limpos');
  }, [addLog]);

  const resetStats = useCallback(() => {
    keywordManager.resetUsedKeywords();
    autoPublisher.cleanup();
    updateStats();
    addLog('🔄 Estatísticas resetadas');
  }, [updateStats, addLog]);

  /**
   * 📊 STATUS GERAL
   */
  const getStatus = useCallback(() => {
    const now = new Date();
    const nextRun = isActive ? new Date(now.getTime() + config.interval) : null;
    
    return {
      isActive,
      isGenerating,
      canGenerateToday: canGenerateToday(),
      nextRun: nextRun?.toISOString(),
      timeUntilNext: nextRun ? nextRun.getTime() - now.getTime() : null,
      totalGenerated: stats.publishing?.totalPublished || 0,
      todayGenerated: stats.publishing?.publishedToday || 0
    };
  }, [isActive, isGenerating, canGenerateToday, config.interval, stats]);

  return {
    // Estado
    isActive,
    isGenerating,
    lastGeneration,
    stats,
    logs,
    config,
    
    // Controles
    startAutomation,
    stopAutomation,
    generateNow,
    updateConfig,
    clearLogs,
    resetStats,
    
    // Utilitários
    getStatus,
    canGenerateToday,
    
    // Dados
    keywordManager,
    autoPublisher,
    contentGenerator
  };
};

export default useAutoBlogger;
