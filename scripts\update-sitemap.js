// Atualizador Automático de Sitemap - SEO Otimizado
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configurações
const SITE_URL = 'https://free-energy-5752f.web.app';
const SITEMAP_PATH = path.join(__dirname, '../client/public/sitemap.xml');

// URLs estáticas do site
const staticUrls = [
  {
    url: '/',
    priority: '1.0',
    changefreq: 'weekly',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/enercy',
    priority: '0.9',
    changefreq: 'monthly',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/blog',
    priority: '0.9',
    changefreq: 'daily',
    lastmod: new Date().toISOString().split('T')[0]
  }
];

// Keywords de alto impacto para páginas dinâmicas
const dynamicPages = [
  'energia-solar-preco-2024',
  'como-funciona-energia-solar',
  'instalacao-energia-solar-residencial',
  'vantagens-energia-solar',
  'financiamento-energia-solar',
  'energia-solar-vale-a-pena',
  'paineis-solares-tipos',
  'energia-solar-apartamento',
  'mercado-livre-energia',
  'economia-energia-solar'
];

// Gerador de sitemap XML
function generateSitemap() {
  console.log('🗺️ Gerando sitemap otimizado...');
  
  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:mobile="http://www.google.com/schemas/sitemap-mobile/1.0"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
`;

  // Adicionar URLs estáticas
  staticUrls.forEach(page => {
    sitemap += `
  <url>
    <loc>${SITE_URL}${page.url}</loc>
    <lastmod>${page.lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
    <mobile:mobile/>
  </url>`;
  });

  // Adicionar páginas do blog (dinâmicas)
  dynamicPages.forEach(slug => {
    sitemap += `
  <url>
    <loc>${SITE_URL}/blog/${slug}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
    <mobile:mobile/>
    <image:image>
      <image:loc>${SITE_URL}/blog/images/${slug}.jpg</image:loc>
      <image:title>${slug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</image:title>
    </image:image>
  </url>`;
  });

  // Adicionar páginas de categorias
  const categories = ['energia-solar', 'economia', 'sustentabilidade', 'instalacao'];
  categories.forEach(category => {
    sitemap += `
  <url>
    <loc>${SITE_URL}/blog/categoria/${category}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
    <mobile:mobile/>
  </url>`;
  });

  sitemap += `
</urlset>`;

  return sitemap;
}

// Gerador de robots.txt otimizado
function generateRobotsTxt() {
  console.log('🤖 Gerando robots.txt otimizado...');
  
  const robots = `# FreeEnergy Brasil - Robots.txt Otimizado
User-agent: *
Allow: /
Allow: /blog/
Allow: /enercy
Allow: /*.css
Allow: /*.js
Allow: /*.png
Allow: /*.jpg
Allow: /*.jpeg
Allow: /*.gif
Allow: /*.svg
Allow: /*.webp

# Bloquear arquivos desnecessários
Disallow: /admin/
Disallow: /api/
Disallow: /*.json$
Disallow: /*?*
Disallow: /debug

# Crawl-delay para bots específicos
User-agent: Bingbot
Crawl-delay: 1

User-agent: Slurp
Crawl-delay: 1

# Sitemap
Sitemap: ${SITE_URL}/sitemap.xml

# Cache policy
Cache-Control: public, max-age=86400
`;

  return robots;
}

// Função principal
async function updateSEOFiles() {
  try {
    console.log('🚀 Iniciando atualização de arquivos SEO...');
    
    // Gerar sitemap
    const sitemapContent = generateSitemap();
    fs.writeFileSync(SITEMAP_PATH, sitemapContent, 'utf8');
    console.log('✅ Sitemap atualizado:', SITEMAP_PATH);
    
    // Gerar robots.txt
    const robotsContent = generateRobotsTxt();
    const robotsPath = path.join(__dirname, '../client/public/robots.txt');
    fs.writeFileSync(robotsPath, robotsContent, 'utf8');
    console.log('✅ Robots.txt atualizado:', robotsPath);
    
    // Estatísticas
    const stats = {
      totalUrls: staticUrls.length + dynamicPages.length + 4, // +4 para categorias
      lastUpdate: new Date().toISOString(),
      sitemapSize: Buffer.byteLength(sitemapContent, 'utf8'),
      robotsSize: Buffer.byteLength(robotsContent, 'utf8')
    };
    
    console.log('📊 Estatísticas SEO:');
    console.log(`   📄 Total de URLs: ${stats.totalUrls}`);
    console.log(`   📅 Última atualização: ${stats.lastUpdate}`);
    console.log(`   📏 Tamanho do sitemap: ${stats.sitemapSize} bytes`);
    console.log(`   🤖 Tamanho do robots.txt: ${stats.robotsSize} bytes`);
    
    // Validar sitemap
    if (stats.sitemapSize > 50000000) { // 50MB limit
      console.warn('⚠️ Sitemap muito grande, considere dividir em múltiplos arquivos');
    }
    
    if (stats.totalUrls > 50000) { // 50k URLs limit
      console.warn('⚠️ Muitas URLs no sitemap, considere usar índice de sitemaps');
    }
    
    console.log('🎉 Arquivos SEO atualizados com sucesso!');
    
    return stats;
    
  } catch (error) {
    console.error('❌ Erro ao atualizar arquivos SEO:', error);
    throw error;
  }
}

// Função para submeter sitemap ao Google
async function submitToGoogle() {
  try {
    console.log('📤 Submetendo sitemap ao Google Search Console...');
    
    // Ping do Google para notificar sobre sitemap atualizado
    const pingUrl = `https://www.google.com/ping?sitemap=${encodeURIComponent(SITE_URL + '/sitemap.xml')}`;
    
    const response = await fetch(pingUrl);
    
    if (response.ok) {
      console.log('✅ Sitemap submetido ao Google com sucesso!');
    } else {
      console.warn('⚠️ Falha ao submeter sitemap ao Google:', response.status);
    }
    
  } catch (error) {
    console.warn('⚠️ Erro ao submeter sitemap ao Google:', error.message);
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  updateSEOFiles()
    .then(submitToGoogle)
    .then(() => {
      console.log('🚀 Processo de atualização SEO concluído!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Falha no processo SEO:', error);
      process.exit(1);
    });
}

export { updateSEOFiles, generateSitemap, generateRobotsTxt, submitToGoogle };
