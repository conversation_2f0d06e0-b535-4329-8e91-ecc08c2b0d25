import {
  leads,
  newsletters,
  blogPosts,
  type Lead,
  type InsertLead,
  type Newsletter,
  type InsertNewsletter,
  type BlogPost,
  type InsertBlogPost,
  type UpdateBlogPost,
  type BlogPostFilters
} from "@shared/schema";

export interface IStorage {
  // Leads
  createLead(lead: InsertLead): Promise<Lead>;
  getAllLeads(): Promise<Lead[]>;

  // Newsletter
  createNewsletter(newsletter: InsertNewsletter): Promise<Newsletter>;
  getNewsletter(email: string): Promise<Newsletter | undefined>;

  // Blog Posts
  createBlogPost(post: InsertBlogPost): Promise<BlogPost>;
  getBlogPost(id: number): Promise<BlogPost | undefined>;
  getBlogPostBySlug(slug: string): Promise<BlogPost | undefined>;
  updateBlogPost(id: number, updates: Partial<UpdateBlogPost>): Promise<BlogPost>;
  deleteBlogPost(id: number): Promise<boolean>;
  getAllBlogPosts(filters?: BlogPostFilters): Promise<{ posts: BlogPost[], total: number }>;
  incrementBlogPostViews(id: number): Promise<void>;
  incrementBlogPostLikes(id: number): Promise<void>;

  // Anti-fake news
  updateFactCheckScore(id: number, score: number, sources?: string[]): Promise<void>;

  // SEO
  updateSEOScore(id: number, score: number): Promise<void>;
}

export class MemStorage implements IStorage {
  private leads: Map<number, Lead>;
  private newsletters: Map<number, Newsletter>;
  private blogPosts: Map<number, BlogPost>;
  private leadId: number;
  private newsletterId: number;
  private blogPostId: number;

  constructor() {
    this.leads = new Map();
    this.newsletters = new Map();
    this.blogPosts = new Map();
    this.leadId = 1;
    this.newsletterId = 1;
    this.blogPostId = 1;

    // Inicializar com alguns posts de exemplo
    this.initializeSamplePosts();
  }

  private initializeSamplePosts() {
    const samplePosts: Omit<BlogPost, 'id'>[] = [
      {
        title: "🔥 URGENTE: Governo Anuncia Fim dos Descontos na Conta de Luz",
        slug: "governo-fim-descontos-conta-luz",
        content: this.generateSampleContent("Governo Anuncia Fim dos Descontos"),
        excerpt: "EXCLUSIVO: Vazou documento interno que revela mudanças drásticas nas tarifas de energia. Veja como se proteger AGORA.",
        metaTitle: "🔥 URGENTE: Governo Anuncia Fim dos Descontos na Conta de Luz | FreeEnergy",
        metaDescription: "EXCLUSIVO: Vazou documento interno que revela mudanças drásticas nas tarifas de energia. Veja como se proteger AGORA.",
        keywords: ["governo", "conta de luz", "descontos", "tarifas", "energia"],
        tags: ["urgente", "governo", "conta de luz"],
        category: "URGENTE",
        author: "FreeEnergy",
        status: "published",
        featured: true,
        viral: true,
        readTime: 3,
        views: 15420,
        likes: 892,
        imageUrl: "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=800&h=400&fit=crop&q=80",
        publishedAt: new Date("2024-12-20"),
        createdAt: new Date("2024-12-20"),
        updatedAt: new Date("2024-12-20"),
        factChecked: true,
        factCheckScore: 85,
        sources: ["gov.br", "aneel.gov.br"],
        aiGenerated: true,
        aiModel: "gemini",
        seoScore: 92,
        socialShares: 1250,
        avgTimeOnPage: 180
      },
      {
        title: "💰 Aposentado Ganha R$ 15.000/Mês com Energia Solar",
        slug: "aposentado-ganha-15000-energia-solar",
        content: this.generateSampleContent("Aposentado Ganha R$ 15.000/Mês"),
        excerpt: "CASO REAL: João, 67 anos, descobriu como transformar energia solar em renda passiva. Em 8 meses já faturou R$ 120.000.",
        metaTitle: "💰 Aposentado Ganha R$ 15.000/Mês com Energia Solar | Caso Real",
        metaDescription: "CASO REAL: João, 67 anos, descobriu como transformar energia solar em renda passiva. Em 8 meses já faturou R$ 120.000.",
        keywords: ["aposentado", "renda extra", "energia solar", "caso real"],
        tags: ["renda extra", "energia solar", "aposentado"],
        category: "Renda Extra",
        author: "FreeEnergy",
        status: "published",
        featured: true,
        viral: true,
        readTime: 7,
        views: 23150,
        likes: 1456,
        imageUrl: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop&q=80",
        publishedAt: new Date("2024-12-19"),
        createdAt: new Date("2024-12-19"),
        updatedAt: new Date("2024-12-19"),
        factChecked: true,
        factCheckScore: 90,
        sources: ["depoimento-verificado", "documentos-comprobatorios"],
        aiGenerated: false,
        aiModel: null,
        seoScore: 88,
        socialShares: 2100,
        avgTimeOnPage: 240
      }
    ];

    samplePosts.forEach(post => {
      const id = this.blogPostId++;
      this.blogPosts.set(id, { ...post, id } as BlogPost);
    });
  }

  private generateSampleContent(title: string): string {
    return `
      <h1>${title}</h1>
      <p>Este é um artigo completo sobre ${title.toLowerCase()}. O conteúdo foi cuidadosamente elaborado para fornecer informações precisas e úteis sobre energia solar e economia de energia.</p>

      <h2>Principais Benefícios</h2>
      <p>A energia solar oferece inúmeras vantagens para residências e empresas brasileiras. Entre os principais benefícios estão a redução significativa na conta de luz, a sustentabilidade ambiental e o retorno do investimento a longo prazo.</p>

      <ul>
        <li>Economia de até 95% na conta de luz</li>
        <li>Valorização do imóvel</li>
        <li>Sustentabilidade ambiental</li>
        <li>Tecnologia confiável e durável</li>
      </ul>

      <h2>Como Funciona na Prática</h2>
      <p>O sistema de energia solar fotovoltaica converte a luz do sol em energia elétrica através de painéis solares instalados no telhado. Esta energia é então convertida por um inversor para ser compatível com a rede elétrica da sua casa.</p>

      <h2>Investimento e Retorno</h2>
      <p>O investimento em energia solar se paga em média entre 4 a 6 anos, e os painéis têm garantia de 25 anos. Isso significa mais de 20 anos de energia praticamente gratuita.</p>

      <div class="cta-section">
        <h3>Quer saber mais?</h3>
        <p>Entre em contato conosco pelo WhatsApp e descubra como a energia solar pode transformar sua conta de luz!</p>
        <a href="https://wa.me/5598981735618" target="_blank" class="btn-whatsapp">💬 Falar no WhatsApp</a>
      </div>
    `;
  }

  async createLead(insertLead: InsertLead): Promise<Lead> {
    const id = this.leadId++;
    const lead: Lead = { 
      ...insertLead, 
      id, 
      createdAt: new Date().toISOString() 
    };
    this.leads.set(id, lead);
    return lead;
  }

  async createNewsletter(insertNewsletter: InsertNewsletter): Promise<Newsletter> {
    // Check if email already exists
    const existingNewsletter = await this.getNewsletter(insertNewsletter.email);
    if (existingNewsletter) {
      return existingNewsletter;
    }

    const id = this.newsletterId++;
    const newsletter: Newsletter = { 
      ...insertNewsletter, 
      id, 
      createdAt: new Date().toISOString() 
    };
    this.newsletters.set(id, newsletter);
    return newsletter;
  }

  async getNewsletter(email: string): Promise<Newsletter | undefined> {
    return Array.from(this.newsletters.values()).find(
      (newsletter) => newsletter.email === email,
    );
  }

  async getAllLeads(): Promise<Lead[]> {
    return Array.from(this.leads.values());
  }

  // Blog Posts Methods
  async createBlogPost(insertPost: InsertBlogPost): Promise<BlogPost> {
    const id = this.blogPostId++;
    const now = new Date();

    // Gerar slug único se necessário
    let slug = insertPost.slug;
    let counter = 1;
    while (Array.from(this.blogPosts.values()).some(post => post.slug === slug)) {
      slug = `${insertPost.slug}-${counter}`;
      counter++;
    }

    const post: BlogPost = {
      ...insertPost,
      id,
      slug,
      views: 0,
      likes: 0,
      socialShares: 0,
      avgTimeOnPage: 0,
      publishedAt: insertPost.status === 'published' ? now : null,
      createdAt: now,
      updatedAt: now,
    };

    this.blogPosts.set(id, post);
    console.log(`✅ Post criado: ${post.title} (ID: ${id})`);
    return post;
  }

  async getBlogPost(id: number): Promise<BlogPost | undefined> {
    return this.blogPosts.get(id);
  }

  async getBlogPostBySlug(slug: string): Promise<BlogPost | undefined> {
    return Array.from(this.blogPosts.values()).find(post => post.slug === slug);
  }

  async updateBlogPost(id: number, updates: Partial<UpdateBlogPost>): Promise<BlogPost> {
    const existingPost = this.blogPosts.get(id);
    if (!existingPost) {
      throw new Error(`Post with ID ${id} not found`);
    }

    const updatedPost: BlogPost = {
      ...existingPost,
      ...updates,
      updatedAt: new Date(),
      publishedAt: updates.status === 'published' && !existingPost.publishedAt
        ? new Date()
        : existingPost.publishedAt
    };

    this.blogPosts.set(id, updatedPost);
    console.log(`✅ Post atualizado: ${updatedPost.title} (ID: ${id})`);
    return updatedPost;
  }

  async deleteBlogPost(id: number): Promise<boolean> {
    const deleted = this.blogPosts.delete(id);
    if (deleted) {
      console.log(`🗑️ Post deletado (ID: ${id})`);
    }
    return deleted;
  }

  async getAllBlogPosts(filters?: BlogPostFilters): Promise<{ posts: BlogPost[], total: number }> {
    let posts = Array.from(this.blogPosts.values());

    // Aplicar filtros
    if (filters) {
      if (filters.category) {
        posts = posts.filter(post => post.category === filters.category);
      }

      if (filters.status) {
        posts = posts.filter(post => post.status === filters.status);
      }

      if (filters.featured !== undefined) {
        posts = posts.filter(post => post.featured === filters.featured);
      }

      if (filters.viral !== undefined) {
        posts = posts.filter(post => post.viral === filters.viral);
      }

      if (filters.factChecked !== undefined) {
        posts = posts.filter(post => post.factChecked === filters.factChecked);
      }

      if (filters.aiGenerated !== undefined) {
        posts = posts.filter(post => post.aiGenerated === filters.aiGenerated);
      }

      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        posts = posts.filter(post =>
          post.title.toLowerCase().includes(searchTerm) ||
          post.excerpt.toLowerCase().includes(searchTerm) ||
          post.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
          post.keywords.some(keyword => keyword.toLowerCase().includes(searchTerm))
        );
      }
    }

    const total = posts.length;

    // Ordenação
    const sortBy = filters?.sortBy || 'publishedAt';
    const sortOrder = filters?.sortOrder || 'desc';

    posts.sort((a, b) => {
      let aValue: any = a[sortBy];
      let bValue: any = b[sortBy];

      // Tratar datas
      if (sortBy === 'publishedAt' || sortBy === 'createdAt') {
        aValue = aValue ? new Date(aValue).getTime() : 0;
        bValue = bValue ? new Date(bValue).getTime() : 0;
      }

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
      } else {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      }
    });

    // Paginação
    const offset = filters?.offset || 0;
    const limit = filters?.limit || 10;
    posts = posts.slice(offset, offset + limit);

    return { posts, total };
  }

  async incrementBlogPostViews(id: number): Promise<void> {
    const post = this.blogPosts.get(id);
    if (post) {
      post.views = (post.views || 0) + 1;
      post.updatedAt = new Date();
      this.blogPosts.set(id, post);
    }
  }

  async incrementBlogPostLikes(id: number): Promise<void> {
    const post = this.blogPosts.get(id);
    if (post) {
      post.likes = (post.likes || 0) + 1;
      post.updatedAt = new Date();
      this.blogPosts.set(id, post);
    }
  }

  async updateFactCheckScore(id: number, score: number, sources?: string[]): Promise<void> {
    const post = this.blogPosts.get(id);
    if (post) {
      post.factCheckScore = Math.max(0, Math.min(100, score));
      post.factChecked = score >= 70; // Considera fact-checked se score >= 70
      if (sources) {
        post.sources = sources;
      }
      post.updatedAt = new Date();
      this.blogPosts.set(id, post);
      console.log(`✅ Fact-check atualizado para post ${id}: ${score}%`);
    }
  }

  async updateSEOScore(id: number, score: number): Promise<void> {
    const post = this.blogPosts.get(id);
    if (post) {
      post.seoScore = Math.max(0, Math.min(100, score));
      post.updatedAt = new Date();
      this.blogPosts.set(id, post);
      console.log(`✅ SEO score atualizado para post ${id}: ${score}%`);
    }
  }
}

export const storage = new MemStorage();
