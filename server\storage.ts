import { 
  leads, 
  newsletters, 
  type Lead, 
  type InsertLead, 
  type Newsletter, 
  type InsertNewsletter 
} from "@shared/schema";

export interface IStorage {
  createLead(lead: InsertLead): Promise<Lead>;
  createNewsletter(newsletter: InsertNewsletter): Promise<Newsletter>;
  getNewsletter(email: string): Promise<Newsletter | undefined>;
  getAllLeads(): Promise<Lead[]>;
}

export class MemStorage implements IStorage {
  private leads: Map<number, Lead>;
  private newsletters: Map<number, Newsletter>;
  private leadId: number;
  private newsletterId: number;

  constructor() {
    this.leads = new Map();
    this.newsletters = new Map();
    this.leadId = 1;
    this.newsletterId = 1;
  }

  async createLead(insertLead: InsertLead): Promise<Lead> {
    const id = this.leadId++;
    const lead: Lead = {
      ...insertLead,
      id,
      createdAt: new Date().toISOString()
    };
    this.leads.set(id, lead);

    // Log para debug (sem custo de BD)
    console.log(`✅ Lead criado: ${lead.name} - ${lead.contact} - Consumo: R$${lead.consumption}`);

    return lead;
  }

  async createNewsletter(insertNewsletter: InsertNewsletter): Promise<Newsletter> {
    // Check if email already exists
    const existingNewsletter = await this.getNewsletter(insertNewsletter.email);
    if (existingNewsletter) {
      console.log(`⚠️ Email já cadastrado: ${insertNewsletter.email}`);
      return existingNewsletter;
    }

    const id = this.newsletterId++;
    const newsletter: Newsletter = {
      ...insertNewsletter,
      id,
      createdAt: new Date().toISOString()
    };
    this.newsletters.set(id, newsletter);

    // Log para debug
    console.log(`📧 Newsletter cadastrado: ${newsletter.email}`);

    return newsletter;
  }

  async getNewsletter(email: string): Promise<Newsletter | undefined> {
    return Array.from(this.newsletters.values()).find(
      (newsletter) => newsletter.email === email,
    );
  }

  async getAllLeads(): Promise<Lead[]> {
    return Array.from(this.leads.values());
  }
}

export const storage = new MemStorage();
