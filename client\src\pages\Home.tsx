import { useEffect } from "react";
import Header from "@/components/Header";
import HeroSection from "@/components/HeroSection";
import StorytellingSection from "@/components/StorytellingSection";
import HowItWorksSection from "@/components/HowItWorksSection";
import AboutSection from "@/components/AboutSection";
import SolutionsSection from "@/components/SolutionsSection";
import SimulatorSection from "@/components/SimulatorSection";
import CasesSection from "@/components/CasesSection";
import DifferentialsSection from "@/components/DifferentialsSection";
import PreLaunchSection from "@/components/PreLaunchSection";
import FutureSection from "@/components/FutureSection";
import ContactSection from "@/components/ContactSection";
import Footer from "@/components/Footer";

export default function Home() {
  useEffect(() => {
    // SEO básico inline para deploy rápido
    document.title = 'FreeEnergy - Economize até 40% na Conta de Luz | Energia Solar e Renovável';

    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', 'Conectamos você às melhores soluções de energia renovável do Brasil. Economize até 40% na conta de luz sem investimento inicial.');
    }
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <div className="pt-16">
        <HeroSection />
        <StorytellingSection />
        <HowItWorksSection />
        <AboutSection />
        <SolutionsSection />
        <SimulatorSection />
        <CasesSection />
        <DifferentialsSection />
        <PreLaunchSection />
        <FutureSection />
        <ContactSection />
      </div>
      <Footer />
    </div>
  );
}