// Pipedream Workflow - Blog Automatizado
// URL: https://pipedream.com/new

export default defineComponent({
  name: "FreeEnergy Auto Blog Generator",
  version: "0.1.0",
  
  async run({ steps, $ }) {
    
    // STEP 1: Trigger - Cron Schedule (3x por semana)
    // Configurar em: Trigger > <PERSON><PERSON> > "0 9 * * 1,3,5"
    
    // STEP 2: Google Trends API - Detectar trending keywords
    const trendingKeywords = await $.send.http({
      method: "GET",
      url: "https://trends.google.com/trends/api/dailytrends",
      params: {
        hl: "pt-BR",
        tz: "-180",
        geo: "BR",
        ns: 15 // Categoria: Energia
      }
    });
    
    // STEP 3: Keyword Selection (Pareto 80/20)
    const highImpactKeywords = [
      "energia solar preço 2024",
      "instalação energia solar residencial",
      "como funciona energia solar",
      "vantagens energia solar",
      "financiamento energia solar",
      "energia solar vale a pena",
      "painéis solares tipos",
      "energia solar apartamento",
      "mercado livre energia",
      "economia energia solar"
    ];
    
    // Combinar trending + high impact
    const selectedKeyword = highImpactKeywords[Math.floor(Math.random() * highImpactKeywords.length)];
    
    // STEP 4: OpenAI Content Generation
    const openaiResponse = await $.send.http({
      method: "POST",
      url: "https://api.openai.com/v1/chat/completions",
      headers: {
        "Authorization": `Bearer ${process.env.OPENAI_API_KEY}`,
        "Content-Type": "application/json"
      },
      data: {
        model: "gpt-3.5-turbo",
        messages: [{
          role: "user",
          content: `
Escreva um artigo completo e otimizado para SEO sobre "${selectedKeyword}" com:

ESTRUTURA:
1. Introdução envolvente (150 palavras)
2. 5-7 seções principais com H2
3. Subsecções com H3 quando necessário
4. Conclusão com CTA
5. FAQ (5 perguntas)

REQUISITOS SEO:
- Palavra-chave principal: "${selectedKeyword}"
- Densidade: 1-2%
- LSI keywords relacionadas
- Meta description atrativa
- Títulos otimizados (H1, H2, H3)
- Mínimo 1500 palavras
- Tom conversacional e autoridade

FOCO:
- Energia solar no Brasil
- Economia na conta de luz
- Sustentabilidade
- Casos práticos
- Dados atualizados 2024

FORMATO: Markdown com frontmatter YAML
          `
        }],
        max_tokens: 3000,
        temperature: 0.7
      }
    });
    
    const generatedContent = openaiResponse.data.choices[0].message.content;
    
    // STEP 5: SEO Optimization
    const slug = selectedKeyword.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, '');
    const title = `${selectedKeyword} | Guia Completo 2024 | FreeEnergy`;
    const description = `✅ Tudo sobre ${selectedKeyword}. Guia completo, preços atualizados e dicas de especialistas. Economize até 95% na conta de luz!`;
    const date = new Date().toISOString().split('T')[0];
    
    const frontmatter = `---
title: "${title}"
description: "${description}"
keywords: "${selectedKeyword}, energia solar, economia energia, sustentabilidade"
author: "FreeEnergy Brasil"
date: "${date}"
slug: "${slug}"
category: "Energia Solar"
tags: ["energia solar", "economia", "sustentabilidade", "brasil"]
image: "https://source.unsplash.com/1200x630/?solar-energy,${encodeURIComponent(selectedKeyword)}"
canonical: "https://free-energy-5752f.web.app/blog/${slug}"
---

`;
    
    const optimizedContent = frontmatter + generatedContent;
    
    // STEP 6: Image Generation (Unsplash)
    const imageUrl = `https://source.unsplash.com/1200x630/?solar-energy,${encodeURIComponent(selectedKeyword)}`;
    
    // STEP 7: Deploy to Firebase via Webhook
    const deployResponse = await $.send.http({
      method: "POST",
      url: "https://us-central1-free-energy-5752f.cloudfunctions.net/deployBlogPost",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.FIREBASE_TOKEN}`
      },
      data: {
        slug: slug,
        content: optimizedContent,
        image: imageUrl,
        title: title,
        description: description,
        keywords: selectedKeyword
      }
    });
    
    // STEP 8: Update Sitemap
    await $.send.http({
      method: "POST",
      url: "https://us-central1-free-energy-5752f.cloudfunctions.net/updateSitemap",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.FIREBASE_TOKEN}`
      },
      data: {
        url: `https://free-energy-5752f.web.app/blog/${slug}`,
        lastmod: date,
        priority: "0.8"
      }
    });
    
    // STEP 9: Social Media Auto-Post (opcional)
    // Twitter API
    if (process.env.TWITTER_ENABLED === 'true') {
      await $.send.http({
        method: "POST",
        url: "https://api.twitter.com/2/tweets",
        headers: {
          "Authorization": `Bearer ${process.env.TWITTER_BEARER_TOKEN}`,
          "Content-Type": "application/json"
        },
        data: {
          text: `🌞 Novo artigo: ${title}\n\n${description.substring(0, 100)}...\n\n🔗 https://free-energy-5752f.web.app/blog/${slug}\n\n#EnergiaSolar #Sustentabilidade #EconomiaEnergia`
        }
      });
    }
    
    // STEP 10: Analytics & Monitoring
    const result = {
      success: true,
      keyword: selectedKeyword,
      slug: slug,
      title: title,
      url: `https://free-energy-5752f.web.app/blog/${slug}`,
      publishedAt: date,
      wordCount: generatedContent.split(' ').length,
      estimatedReadTime: Math.ceil(generatedContent.split(' ').length / 200)
    };
    
    // Log para monitoramento
    console.log('✅ Blog post gerado automaticamente:', result);
    
    // STEP 11: Webhook para Google Search Console (indexação rápida)
    if (process.env.GOOGLE_INDEXING_ENABLED === 'true') {
      await $.send.http({
        method: "POST",
        url: "https://indexing.googleapis.com/v3/urlNotifications:publish",
        headers: {
          "Authorization": `Bearer ${process.env.GOOGLE_INDEXING_TOKEN}`,
          "Content-Type": "application/json"
        },
        data: {
          url: `https://free-energy-5752f.web.app/blog/${slug}`,
          type: "URL_UPDATED"
        }
      });
    }
    
    return result;
  }
});

// CONFIGURAÇÃO PIPEDREAM:
// 1. Criar conta gratuita: https://pipedream.com
// 2. Novo workflow > Cron trigger
// 3. Adicionar este código
// 4. Configurar variáveis de ambiente:
//    - OPENAI_API_KEY
//    - FIREBASE_TOKEN  
//    - TWITTER_BEARER_TOKEN (opcional)
//    - GOOGLE_INDEXING_TOKEN (opcional)
// 5. Ativar workflow

// CUSTOS:
// - Pipedream: Gratuito (10k execuções/mês)
// - OpenAI: ~$0.002 por artigo
// - Firebase: Gratuito
// - Total: ~$0.60/mês para 3 artigos/semana
