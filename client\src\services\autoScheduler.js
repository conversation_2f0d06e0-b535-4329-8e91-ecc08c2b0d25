/**
 * ⏰ SISTEMA DE AGENDAMENTO AUTOMÁTICO - FREEENERGY
 * Publica posts automaticamente em horários otimizados
 */

import intelligentContentGenerator from './intelligentContentGenerator.js';

class AutoScheduler {
  constructor() {
    this.isRunning = false;
    this.intervalId = null;
    this.publishQueue = [];
    
    // Configurações de agendamento
    this.config = {
      enabled: false,
      interval: 24 * 60 * 60 * 1000, // 24 horas em ms
      maxPostsPerDay: 2,
      optimalHours: [9, 14, 19], // Horários de maior engajamento
      categories: ['Energia Solar', 'Economia', 'Sustentabilidade', 'Tecnologia'],
      minFactCheckScore: 75,
      minSEOScore: 70
    };
    
    // Keywords para rotação automática
    this.keywordPool = [
      'energia solar residencial',
      'economia conta luz',
      'painéis solares preço',
      'energia solar vale pena',
      'instalação energia solar',
      'financiamento energia solar',
      'energia solar apartamento',
      'energia renovável brasil',
      'sustentabilidade energia',
      'investimento energia solar'
    ];
    
    this.usedKeywords = new Set();
    this.loadConfig();
    this.loadPublishQueue();
  }

  /**
   * 🚀 INICIAR SISTEMA AUTOMÁTICO
   */
  start() {
    if (this.isRunning) {
      console.log('⚠️ AutoScheduler já está rodando');
      return;
    }

    console.log('🚀 Iniciando AutoScheduler...');
    this.isRunning = true;
    this.config.enabled = true;
    
    // Verificar imediatamente se precisa publicar
    this.checkAndPublish();
    
    // Configurar intervalo de verificação (a cada hora)
    this.intervalId = setInterval(() => {
      this.checkAndPublish();
    }, 60 * 60 * 1000); // 1 hora
    
    this.saveConfig();
    console.log('✅ AutoScheduler iniciado com sucesso!');
  }

  /**
   * ⏹️ PARAR SISTEMA AUTOMÁTICO
   */
  stop() {
    if (!this.isRunning) {
      console.log('⚠️ AutoScheduler já está parado');
      return;
    }

    console.log('⏹️ Parando AutoScheduler...');
    this.isRunning = false;
    this.config.enabled = false;
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
    this.saveConfig();
    console.log('✅ AutoScheduler parado com sucesso!');
  }

  /**
   * 🔍 VERIFICAR E PUBLICAR SE NECESSÁRIO
   */
  async checkAndPublish() {
    if (!this.config.enabled) return;

    console.log('🔍 Verificando se deve publicar novo post...');
    
    try {
      const now = new Date();
      const currentHour = now.getHours();
      
      // Verificar se é um horário otimizado
      if (!this.config.optimalHours.includes(currentHour)) {
        console.log(`⏰ Horário não otimizado (${currentHour}h). Aguardando...`);
        return;
      }
      
      // Verificar quantos posts foram publicados hoje
      const todayPosts = this.getTodayPostsCount();
      if (todayPosts >= this.config.maxPostsPerDay) {
        console.log(`📊 Limite diário atingido (${todayPosts}/${this.config.maxPostsPerDay})`);
        return;
      }
      
      // Verificar se há posts na fila
      if (this.publishQueue.length > 0) {
        await this.publishFromQueue();
      } else {
        // Gerar novo post automaticamente
        await this.generateAndPublishPost();
      }
      
    } catch (error) {
      console.error('❌ Erro no checkAndPublish:', error);
    }
  }

  /**
   * 📝 GERAR E PUBLICAR POST AUTOMATICAMENTE
   */
  async generateAndPublishPost() {
    console.log('📝 Gerando novo post automaticamente...');
    
    try {
      // Selecionar keyword
      const keyword = this.selectNextKeyword();
      const category = this.selectRandomCategory();
      
      console.log(`🎯 Gerando post para: ${keyword} (${category})`);
      
      // Gerar conteúdo
      const article = await intelligentContentGenerator.generateIntelligentArticle(keyword, category);
      
      // Validar qualidade
      if (!this.validateArticleQuality(article)) {
        console.log('⚠️ Artigo não passou na validação de qualidade');
        return;
      }
      
      // Publicar
      await this.publishArticle(article);
      
      // Marcar keyword como usada
      this.usedKeywords.add(keyword);
      this.saveUsedKeywords();
      
      console.log('✅ Post gerado e publicado automaticamente!');
      
    } catch (error) {
      console.error('❌ Erro ao gerar e publicar post:', error);
    }
  }

  /**
   * 📤 PUBLICAR DA FILA
   */
  async publishFromQueue() {
    const post = this.publishQueue.shift();
    console.log(`📤 Publicando da fila: ${post.title}`);
    
    try {
      await this.publishArticle(post);
      this.savePublishQueue();
      console.log('✅ Post da fila publicado com sucesso!');
    } catch (error) {
      console.error('❌ Erro ao publicar da fila:', error);
      // Recolocar na fila em caso de erro
      this.publishQueue.unshift(post);
    }
  }

  /**
   * 🚀 PUBLICAR ARTIGO
   */
  async publishArticle(article) {
    try {
      // Preparar dados para API
      const postData = {
        title: article.title,
        slug: article.slug,
        content: article.content,
        excerpt: article.excerpt,
        metaTitle: article.title,
        metaDescription: article.metaDescription,
        keywords: article.keywords || [],
        tags: article.tags || [],
        category: article.category,
        author: article.author || 'FreeEnergy',
        status: 'published',
        featured: article.featured || false,
        viral: article.viral || false,
        readTime: article.readTime || 5,
        imageUrl: this.selectRandomImage(),
        factChecked: article.factChecked || false,
        factCheckScore: article.factCheckScore || 0,
        sources: article.sources || [],
        aiGenerated: article.aiGenerated || true,
        aiModel: article.aiModel || 'gemini',
        seoScore: article.seoScore || 0
      };

      // Chamar API do backend
      const response = await fetch('/api/blog/posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(postData)
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Post publicado via API:', result.data.id);
      
      // Disparar evento para atualizar UI
      window.dispatchEvent(new CustomEvent('blogPostsUpdated', {
        detail: { newPost: result.data }
      }));
      
      // Salvar no localStorage também (backup)
      this.saveToLocalStorage(result.data);
      
      return result.data;
      
    } catch (error) {
      console.error('❌ Erro ao publicar artigo:', error);
      
      // Fallback: salvar apenas no localStorage
      const fallbackPost = {
        ...article,
        id: Date.now(),
        publishedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        views: 0,
        likes: 0,
        socialShares: 0,
        avgTimeOnPage: 0
      };
      
      this.saveToLocalStorage(fallbackPost);
      
      window.dispatchEvent(new CustomEvent('blogPostsUpdated', {
        detail: { newPost: fallbackPost }
      }));
      
      return fallbackPost;
    }
  }

  /**
   * 💾 SALVAR NO LOCALSTORAGE
   */
  saveToLocalStorage(post) {
    try {
      const existingPosts = JSON.parse(localStorage.getItem('freeenergy_generated_posts') || '[]');
      existingPosts.unshift(post);
      
      // Manter apenas os últimos 50 posts
      const limitedPosts = existingPosts.slice(0, 50);
      localStorage.setItem('freeenergy_generated_posts', JSON.stringify(limitedPosts));
      
      console.log('💾 Post salvo no localStorage');
    } catch (error) {
      console.error('❌ Erro ao salvar no localStorage:', error);
    }
  }

  /**
   * 🎯 SELECIONAR PRÓXIMA KEYWORD
   */
  selectNextKeyword() {
    const availableKeywords = this.keywordPool.filter(k => !this.usedKeywords.has(k));
    
    if (availableKeywords.length === 0) {
      // Reset se todas foram usadas
      this.usedKeywords.clear();
      this.saveUsedKeywords();
      return this.keywordPool[Math.floor(Math.random() * this.keywordPool.length)];
    }
    
    return availableKeywords[Math.floor(Math.random() * availableKeywords.length)];
  }

  /**
   * 🎲 SELECIONAR CATEGORIA ALEATÓRIA
   */
  selectRandomCategory() {
    return this.config.categories[Math.floor(Math.random() * this.config.categories.length)];
  }

  /**
   * 🖼️ SELECIONAR IMAGEM ALEATÓRIA
   */
  selectRandomImage() {
    const images = [
      'https://images.unsplash.com/photo-1509391366360-2e959784a276?w=800&h=400&fit=crop&q=80',
      'https://images.unsplash.com/photo-1497440001374-f26997328c1b?w=800&h=400&fit=crop&q=80',
      'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=400&fit=crop&q=80',
      'https://images.unsplash.com/photo-1466611653911-95081537e5b7?w=800&h=400&fit=crop&q=80',
      'https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?w=800&h=400&fit=crop&q=80'
    ];
    return images[Math.floor(Math.random() * images.length)];
  }

  /**
   * ✅ VALIDAR QUALIDADE DO ARTIGO
   */
  validateArticleQuality(article) {
    if (!article.title || article.title.length < 20) return false;
    if (!article.content || article.content.length < 1000) return false;
    if (article.factCheckScore < this.config.minFactCheckScore) return false;
    if (article.seoScore < this.config.minSEOScore) return false;
    return true;
  }

  /**
   * 📊 CONTAR POSTS DE HOJE
   */
  getTodayPostsCount() {
    try {
      const today = new Date().toDateString();
      const posts = JSON.parse(localStorage.getItem('freeenergy_generated_posts') || '[]');
      return posts.filter(post => {
        const postDate = new Date(post.publishedAt || post.createdAt).toDateString();
        return postDate === today;
      }).length;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 💾 SALVAR/CARREGAR CONFIGURAÇÕES
   */
  saveConfig() {
    localStorage.setItem('autoscheduler_config', JSON.stringify(this.config));
  }

  loadConfig() {
    try {
      const saved = localStorage.getItem('autoscheduler_config');
      if (saved) {
        this.config = { ...this.config, ...JSON.parse(saved) };
      }
    } catch (error) {
      console.error('❌ Erro ao carregar config:', error);
    }
  }

  savePublishQueue() {
    localStorage.setItem('autoscheduler_queue', JSON.stringify(this.publishQueue));
  }

  loadPublishQueue() {
    try {
      const saved = localStorage.getItem('autoscheduler_queue');
      if (saved) {
        this.publishQueue = JSON.parse(saved);
      }
    } catch (error) {
      console.error('❌ Erro ao carregar fila:', error);
    }
  }

  saveUsedKeywords() {
    localStorage.setItem('autoscheduler_used_keywords', JSON.stringify([...this.usedKeywords]));
  }

  loadUsedKeywords() {
    try {
      const saved = localStorage.getItem('autoscheduler_used_keywords');
      if (saved) {
        this.usedKeywords = new Set(JSON.parse(saved));
      }
    } catch (error) {
      console.error('❌ Erro ao carregar keywords usadas:', error);
    }
  }

  /**
   * 📊 STATUS DO SISTEMA
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      enabled: this.config.enabled,
      queueLength: this.publishQueue.length,
      todayPosts: this.getTodayPostsCount(),
      maxPostsPerDay: this.config.maxPostsPerDay,
      usedKeywords: this.usedKeywords.size,
      totalKeywords: this.keywordPool.length,
      nextOptimalHour: this.getNextOptimalHour()
    };
  }

  getNextOptimalHour() {
    const now = new Date();
    const currentHour = now.getHours();
    
    for (const hour of this.config.optimalHours) {
      if (hour > currentHour) {
        return hour;
      }
    }
    
    // Se passou de todos os horários de hoje, retorna o primeiro de amanhã
    return this.config.optimalHours[0];
  }
}

// Exportar instância singleton
const autoScheduler = new AutoScheduler();
export default autoScheduler;
