/**
 * 🧪 TESTE DIRETO DA GEMINI API - FREEENERGY
 * Testa a chave da API e gera um artigo de exemplo
 */

const testGeminiAPI = async () => {
  const apiKey = 'AIzaSyDkXmv8NDUJSspRXptExlvS-yaV9J_0yBE';
  const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${apiKey}`;
  
  const prompt = `
🎯 ESPECIALISTA EM ENERGIA SOLAR E MARKETING DIGITAL - FREEENERGY BRASIL

MISSÃO CRÍTICA: Criar artigo VIRAL sobre "energia solar residencial" que:
✅ ATRAIA milhares de cliques
✅ CONVERTA leitores em leads qualificados  
✅ POSICIONE #1 no Google para a keyword

📋 ESPECIFICAÇÕES TÉCNICAS:
- Título: Máximo 60 caracteres + emojis + números + urgência
- Meta Description: EXATOS 155 caracteres + CTA irresistível
- Conteúdo: 800 palavras + linguagem persuasiva
- Estrutura: H1, H2, H3 + listas + depoimentos + dados

🎨 TOM E ESTILO:
- URGENTE e persuasivo
- Baseado em dados REAIS do mercado brasileiro
- Linguagem acessível mas técnica
- Foco em ECONOMIA e ROI

🔥 ELEMENTOS OBRIGATÓRIOS:
- Casos reais de economia (nomes, valores, cidades)
- Dados específicos do mercado brasileiro de energia solar
- Benefícios financeiros com números exatos
- Urgência (mudanças legislação, preços, oportunidades)
- CTAs estratégicos para WhatsApp: +55 (98) 98173-5618
- Depoimentos convincentes
- Comparações antes/depois

🚨 FORMATO DE RESPOSTA (JSON VÁLIDO):
{
  "title": "título viral com emojis e urgência",
  "metaDescription": "descrição persuasiva de 155 caracteres exatos",
  "slug": "energia-solar-residencial-economia",
  "content": "HTML completo com estrutura SEO",
  "tags": ["energia solar residencial", "energia solar", "economia", "sustentabilidade", "conta de luz"],
  "category": "Energia Solar",
  "readTime": 6,
  "viral": true,
  "featured": true
}

🎯 KEYWORD PRINCIPAL: "energia solar residencial"
🏢 EMPRESA: FreeEnergy Brasil
📱 WHATSAPP: +55 (98) 98173-5618
🌐 SITE: https://free-energy-5752f.web.app

IMPORTANTE: Responda APENAS com o JSON válido, sem texto adicional.
`;

  try {
    console.log('🧪 Testando Gemini API...');
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.8,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 4000,
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH", 
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          }
        ]
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('❌ Erro na API:', response.status, errorData);
      return { success: false, error: `API Error: ${response.status}` };
    }

    const data = await response.json();
    console.log('✅ Resposta da API recebida:', data);
    
    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      console.error('❌ Resposta inválida da API');
      return { success: false, error: 'Resposta inválida da API' };
    }
    
    const content = data.candidates[0].content.parts[0].text;
    console.log('📝 Conteúdo gerado:', content);
    
    // Tenta fazer parse do JSON
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const article = JSON.parse(jsonMatch[0]);
        console.log('✅ Artigo parseado com sucesso:', article.title);
        return { success: true, article };
      } else {
        console.log('⚠️ JSON não encontrado, usando conteúdo bruto');
        return { success: true, content };
      }
    } catch (parseError) {
      console.log('⚠️ Erro no parse JSON, usando conteúdo bruto');
      return { success: true, content };
    }
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
    return { success: false, error: error.message };
  }
};

// Exporta para uso global
if (typeof window !== 'undefined') {
  window.testGeminiAPI = testGeminiAPI;
}

export default testGeminiAPI;
