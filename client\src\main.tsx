import React from "react";
import ReactDOM from "react-dom/client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import App from "./App";
import "./index.css";

console.log("🚀 FREEENERGY - Carregando React App...");

const queryClient = new QueryClient();

const rootElement = document.getElementById("root");

if (!rootElement) {
  console.error("❌ Root element not found");
  throw new Error("Root element not found");
}

console.log("✅ Root element encontrado, carregando React App...");

// Esconder loading screen quando React carregar
if ((window as any).hideLoading) {
  (window as any).hideLoading();
}

ReactDOM.createRoot(rootElement).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <App />
    </QueryClientProvider>
  </React.StrictMode>
);
