console.log("🚀 FREEENERGY - SITE ULTRA SOFISTICADO E VISUALMENTE IMPRESSIONANTE!");

// Importar sistema de auto-start do Gemini Agent
import('./utils/autoStart.js').catch(console.error);

const rootElement = document.getElementById("root");

if (!rootElement) {
  console.error("❌ Root element not found");
  throw new Error("Root element not found");
}

console.log("✅ Root element encontrado, carregando site ultra sofisticado...");

try {
  // Site ultra sofisticado e visualmente impressionante
  rootElement.innerHTML = `
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');

      * { margin: 0; padding: 0; box-sizing: border-box; }

      body {
        font-family: 'Inter', sans-serif;
        line-height: 1.6;
        color: #ffffff;
        overflow-x: hidden;
        background: #000000;
      }

      /* Partículas de fundo animadas */
      .particles {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1;
      }

      .particle {
        position: absolute;
        width: 3px;
        height: 3px;
        background: linear-gradient(45deg, #00f5ff, #ff00ff, #00ff88);
        border-radius: 50%;
        animation: particleFloat 15s infinite linear;
        opacity: 0.8;
      }

      @keyframes particleFloat {
        0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
        10% { opacity: 0.8; }
        90% { opacity: 0.8; }
        100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
      }

      /* Botões premium com efeitos 3D */
      .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        color: white;
        padding: 18px 36px;
        border-radius: 50px;
        text-decoration: none;
        font-weight: 800;
        display: inline-flex;
        align-items: center;
        gap: 12px;
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow:
          0 10px 40px rgba(102, 126, 234, 0.4),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border: none;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 2px;
        font-size: 14px;
        transform-style: preserve-3d;
      }

      .btn-primary::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        transition: left 0.6s;
      }

      .btn-primary:hover::before {
        left: 100%;
      }

      .btn-primary:hover {
        transform: translateY(-6px) rotateX(10deg) scale(1.05);
        box-shadow:
          0 20px 60px rgba(102, 126, 234, 0.6),
          0 0 0 1px rgba(255, 255, 255, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.3);
      }

      .btn-secondary {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
        color: #1a1a1a;
        padding: 18px 36px;
        border-radius: 50px;
        text-decoration: none;
        font-weight: 800;
        display: inline-flex;
        align-items: center;
        gap: 12px;
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow:
          0 10px 40px rgba(255, 154, 158, 0.4),
          inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border: none;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 2px;
        font-size: 14px;
        transform-style: preserve-3d;
      }

      .btn-secondary:hover {
        transform: translateY(-6px) rotateX(10deg) scale(1.05);
        box-shadow:
          0 20px 60px rgba(255, 154, 158, 0.6),
          0 0 0 1px rgba(255, 255, 255, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.4);
      }

      /* Animações avançadas */
      .floating-animation {
        animation: floating 8s ease-in-out infinite;
      }

      @keyframes floating {
        0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
        25% { transform: translateY(-30px) rotate(3deg) scale(1.05); }
        50% { transform: translateY(-15px) rotate(-2deg) scale(1.02); }
        75% { transform: translateY(-25px) rotate(1deg) scale(1.03); }
      }

      .pulse-glow {
        animation: pulseGlow 4s ease-in-out infinite;
      }

      @keyframes pulseGlow {
        0%, 100% {
          box-shadow:
            0 0 30px rgba(102, 126, 234, 0.5),
            0 0 60px rgba(102, 126, 234, 0.3),
            inset 0 0 30px rgba(102, 126, 234, 0.1);
        }
        50% {
          box-shadow:
            0 0 50px rgba(102, 126, 234, 0.8),
            0 0 100px rgba(102, 126, 234, 0.5),
            0 0 150px rgba(102, 126, 234, 0.3),
            inset 0 0 50px rgba(102, 126, 234, 0.2);
        }
      }

      .card-hover {
        transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transform-style: preserve-3d;
      }

      .card-hover::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(255, 154, 158, 0.2) 100%);
        opacity: 0;
        transition: opacity 0.4s ease;
        z-index: 1;
      }

      .card-hover:hover::before {
        opacity: 1;
      }

      .card-hover:hover {
        transform: translateY(-20px) rotateX(15deg) rotateY(5deg) scale(1.02);
        box-shadow:
          0 40px 80px rgba(0,0,0,0.3),
          0 0 0 1px rgba(255, 255, 255, 0.1);
      }

      .text-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #00f5ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-family: 'Playfair Display', serif;
        background-size: 200% 200%;
        animation: gradientShift 3s ease-in-out infinite;
      }

      @keyframes gradientShift {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
      }

      .glass-effect {
        background: rgba(255, 255, 255, 0.03);
        backdrop-filter: blur(25px);
        border: 1px solid rgba(255, 255, 255, 0.08);
        box-shadow:
          0 8px 32px rgba(0, 0, 0, 0.4),
          inset 0 1px 0 rgba(255, 255, 255, 0.1);
      }

      /* Efeito de texto neon avançado */
      .neon-text {
        color: #fff;
        text-shadow:
          0 0 5px #00f5ff,
          0 0 10px #00f5ff,
          0 0 15px #00f5ff,
          0 0 20px #00f5ff,
          0 0 35px #00f5ff;
        animation: neonFlicker 3s infinite alternate;
      }

      @keyframes neonFlicker {
        0%, 100% {
          opacity: 1;
          text-shadow:
            0 0 5px #00f5ff,
            0 0 10px #00f5ff,
            0 0 15px #00f5ff,
            0 0 20px #00f5ff,
            0 0 35px #00f5ff;
        }
        50% {
          opacity: 0.8;
          text-shadow:
            0 0 2px #00f5ff,
            0 0 5px #00f5ff,
            0 0 8px #00f5ff,
            0 0 12px #00f5ff,
            0 0 20px #00f5ff;
        }
      }

      /* Gradientes de fundo ultra complexos */
      .hero-bg {
        background:
          radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.4) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.4) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.4) 0%, transparent 50%),
          radial-gradient(circle at 60% 80%, rgba(255, 200, 87, 0.3) 0%, transparent 50%),
          linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0a0a0a 100%);
        position: relative;
      }

      .hero-bg::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
          linear-gradient(45deg, transparent 30%, rgba(102, 126, 234, 0.1) 50%, transparent 70%),
          linear-gradient(-45deg, transparent 30%, rgba(255, 154, 158, 0.1) 50%, transparent 70%);
        animation: backgroundShift 10s ease-in-out infinite;
      }

      @keyframes backgroundShift {
        0%, 100% { opacity: 0.5; transform: translateX(0) translateY(0); }
        50% { opacity: 0.8; transform: translateX(20px) translateY(-20px); }
      }

      @media (max-width: 768px) {
        .hero-title { font-size: 2.5rem !important; }
        .nav-desktop { display: none !important; }
        .btn-primary, .btn-secondary { padding: 14px 28px; font-size: 12px; letter-spacing: 1px; }
      }
    </style>

    <!-- Partículas animadas de fundo -->
    <div class="particles">
      <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
      <div class="particle" style="left: 20%; animation-delay: 2s;"></div>
      <div class="particle" style="left: 30%; animation-delay: 4s;"></div>
      <div class="particle" style="left: 40%; animation-delay: 6s;"></div>
      <div class="particle" style="left: 50%; animation-delay: 8s;"></div>
      <div class="particle" style="left: 60%; animation-delay: 10s;"></div>
      <div class="particle" style="left: 70%; animation-delay: 12s;"></div>
      <div class="particle" style="left: 80%; animation-delay: 14s;"></div>
      <div class="particle" style="left: 90%; animation-delay: 16s;"></div>
    </div>

    <!-- Header Ultra Sofisticado -->
    <header style="position: fixed; top: 0; width: 100%; z-index: 1000; background: rgba(0, 0, 0, 0.8); backdrop-filter: blur(30px); border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
      <div style="max-width: 1400px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center; padding: 20px 30px;">
        <div style="display: flex; align-items: center;">
          <a href="/" style="font-size: 2rem; font-weight: 900; text-decoration: none; display: flex; align-items: center; gap: 12px;">
            <div class="pulse-glow floating-animation" style="width: 50px; height: 50px; background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); border-radius: 15px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 1.5rem;">⚡</div>
            <span class="text-gradient" style="font-family: 'Playfair Display', serif;">Free</span><span class="neon-text" style="font-family: 'Playfair Display', serif;">Energy</span>
          </a>
        </div>
        <nav class="nav-desktop" style="display: flex; align-items: center; gap: 40px;">
          <a href="#como-funciona" style="color: rgba(255,255,255,0.8); text-decoration: none; font-weight: 600; transition: all 0.3s; position: relative; text-transform: uppercase; letter-spacing: 1px; font-size: 14px;">Como Funciona</a>
          <a href="#about" style="color: rgba(255,255,255,0.8); text-decoration: none; font-weight: 600; transition: all 0.3s; position: relative; text-transform: uppercase; letter-spacing: 1px; font-size: 14px;">Sobre</a>
          <a href="#simulador" style="color: rgba(255,255,255,0.8); text-decoration: none; font-weight: 600; transition: all 0.3s; position: relative; text-transform: uppercase; letter-spacing: 1px; font-size: 14px;">Simulador</a>
          <a href="#cases" style="color: rgba(255,255,255,0.8); text-decoration: none; font-weight: 600; transition: all 0.3s; position: relative; text-transform: uppercase; letter-spacing: 1px; font-size: 14px;">Cases</a>
        </nav>
        <a href="https://wa.me/5598981735618?text=Olá! Gostaria de saber mais sobre as soluções de energia da Free Energy."
           target="_blank" rel="noopener noreferrer"
           style="background: linear-gradient(135deg, #25D366 0%, #128C7E 100%); color: white; padding: 14px 24px; border-radius: 25px; font-weight: 700; text-decoration: none; transition: all 0.4s; box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4); text-transform: uppercase; letter-spacing: 1px; font-size: 12px;">
          💬 WhatsApp
        </a>
      </div>
    </header>

    <!-- Hero Section Ultra Impressionante -->
    <section class="hero-bg" style="padding: 140px 0 100px; color: white; text-align: center; position: relative; overflow: hidden; min-height: 100vh; display: flex; align-items: center;">
      <div style="max-width: 1400px; margin: 0 auto; padding: 0 30px; position: relative; z-index: 10;">
        <div class="floating-animation pulse-glow" style="margin-bottom: 40px;">
          <div style="width: 120px; height: 120px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(255, 154, 158, 0.3) 100%); border-radius: 30px; display: flex; align-items: center; justify-content: center; margin: 0 auto; font-size: 4rem; backdrop-filter: blur(20px); border: 2px solid rgba(255, 255, 255, 0.1);">⚡</div>
        </div>
        <h1 class="hero-title neon-text" style="font-size: 5rem; font-weight: 900; margin-bottom: 30px; line-height: 1.1; font-family: 'Playfair Display', serif;">
          Economize até <span class="text-gradient" style="font-size: 5.5rem; text-shadow: 0 0 30px rgba(255, 193, 7, 0.8);">40%</span> na Conta de Luz
        </h1>
        <p style="font-size: 1.5rem; margin-bottom: 50px; max-width: 900px; margin-left: auto; margin-right: auto; opacity: 0.9; line-height: 1.8; font-weight: 300;">
          🌱 A <strong class="text-gradient">FreeEnergy</strong> é a plataforma que conecta você com as melhores soluções de energia limpa e barata do Brasil.
          <strong class="neon-text">Educação gratuita</strong>, parceiros especializados e <strong class="text-gradient">liberdade total de escolha</strong> para sua energia.
        </p>
        <div style="display: flex; flex-wrap: wrap; gap: 25px; justify-content: center; margin-bottom: 80px;">
          <a href="https://www.xforcepromotora.com.br/assinatura-energia?affilliate_id=YOCNIH"
             target="_blank" rel="noopener noreferrer" class="btn-primary">
            🚀 Economizar Agora
          </a>
          <a href="#simulador" class="btn-secondary">
            🧮 Simular Economia
          </a>
        </div>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px; max-width: 1000px; margin: 0 auto;">
          <div class="glass-effect card-hover" style="padding: 30px; border-radius: 20px; text-align: center; position: relative; z-index: 2;">
            <div style="font-size: 3rem; margin-bottom: 15px; filter: drop-shadow(0 0 10px rgba(102, 126, 234, 0.5));">🎓</div>
            <div style="font-weight: 700; font-size: 1.2rem; margin-bottom: 8px; color: #fff;">100% Educativo</div>
            <div style="opacity: 0.8; font-size: 1rem; color: rgba(255,255,255,0.7);">Aprenda gratuitamente</div>
          </div>
          <div class="glass-effect card-hover" style="padding: 30px; border-radius: 20px; text-align: center; position: relative; z-index: 2;">
            <div style="font-size: 3rem; margin-bottom: 15px; filter: drop-shadow(0 0 10px rgba(255, 154, 158, 0.5));">🔗</div>
            <div style="font-weight: 700; font-size: 1.2rem; margin-bottom: 8px; color: #fff;">Conectamos Você</div>
            <div style="opacity: 0.8; font-size: 1rem; color: rgba(255,255,255,0.7);">Com os melhores parceiros</div>
          </div>
          <div class="glass-effect card-hover" style="padding: 30px; border-radius: 20px; text-align: center; position: relative; z-index: 2;">
            <div style="font-size: 3rem; margin-bottom: 15px; filter: drop-shadow(0 0 10px rgba(255, 200, 87, 0.5));">💰</div>
            <div style="font-weight: 700; font-size: 1.2rem; margin-bottom: 8px; color: #fff;">Economia Real</div>
            <div style="opacity: 0.8; font-size: 1rem; color: rgba(255,255,255,0.7);">Até 40% de desconto</div>
          </div>
        </div>
      </div>

      <!-- Efeitos visuais adicionais -->
      <div style="position: absolute; top: 10%; left: 10%; width: 200px; height: 200px; background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%); border-radius: 50%; animation: floating 12s ease-in-out infinite; animation-delay: 2s;"></div>
      <div style="position: absolute; top: 60%; right: 15%; width: 150px; height: 150px; background: radial-gradient(circle, rgba(255, 154, 158, 0.3) 0%, transparent 70%); border-radius: 50%; animation: floating 10s ease-in-out infinite; animation-delay: 4s;"></div>
      <div style="position: absolute; bottom: 20%; left: 20%; width: 100px; height: 100px; background: radial-gradient(circle, rgba(120, 219, 255, 0.3) 0%, transparent 70%); border-radius: 50%; animation: floating 8s ease-in-out infinite; animation-delay: 6s;"></div>
    </section>
