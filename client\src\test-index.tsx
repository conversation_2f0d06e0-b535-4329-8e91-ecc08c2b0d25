import React from "react";
import ReactDOM from "react-dom/client";
import TestApp from "./test-app";

console.log("🚀 TESTE - Iniciando React App...");

const rootElement = document.getElementById("root");

if (!rootElement) {
  console.error("❌ Root element not found");
  throw new Error("Root element not found");
}

console.log("✅ Root element encontrado, carregando Test App...");

// Esconder loading screen
if ((window as any).hideLoading) {
  (window as any).hideLoading();
}

try {
  ReactDOM.createRoot(rootElement).render(
    <React.StrictMode>
      <TestApp />
    </React.StrictMode>
  );
  console.log("✅ Test App renderizado com sucesso!");
} catch (error) {
  console.error("❌ Erro ao renderizar Test App:", error);
}
