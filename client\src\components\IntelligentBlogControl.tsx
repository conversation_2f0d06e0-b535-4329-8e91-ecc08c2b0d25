import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Play, 
  Pause, 
  Settings, 
  BarChart3, 
  Check<PERSON>ircle, 
  AlertTriangle,
  <PERSON>,
  Zap,
  Brain,
  Shield
} from 'lucide-react';

// Importar serviços (ser<PERSON> carregado dinamicamente)
let autoScheduler: any = null;
let intelligentContentGenerator: any = null;
let factChecker: any = null;

const IntelligentBlogControl: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [status, setStatus] = useState<any>({});
  const [stats, setStats] = useState({
    totalPosts: 0,
    todayPosts: 0,
    avgFactCheckScore: 0,
    avgSEOScore: 0
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [lastGenerated, setLastGenerated] = useState<any>(null);

  useEffect(() => {
    initializeServices();
    updateStatus();
    
    // Atualizar status a cada 30 segundos
    const interval = setInterval(updateStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const initializeServices = async () => {
    try {
      // Carregar serviços dinamicamente
      const [schedulerModule, generatorModule, checkerModule] = await Promise.all([
        import('../services/autoScheduler.js'),
        import('../services/intelligentContentGenerator.js'),
        import('../services/factChecker.js')
      ]);
      
      autoScheduler = schedulerModule.default;
      intelligentContentGenerator = generatorModule.default;
      factChecker = checkerModule.default;
      
      setIsLoading(false);
      console.log('✅ Serviços do blog inteligente carregados');
    } catch (error) {
      console.error('❌ Erro ao carregar serviços:', error);
      setIsLoading(false);
    }
  };

  const updateStatus = () => {
    if (!autoScheduler) return;
    
    try {
      const currentStatus = autoScheduler.getStatus();
      setStatus(currentStatus);
      
      // Atualizar estatísticas
      updateStats();
    } catch (error) {
      console.error('❌ Erro ao atualizar status:', error);
    }
  };

  const updateStats = () => {
    try {
      const posts = JSON.parse(localStorage.getItem('freeenergy_generated_posts') || '[]');
      const totalPosts = posts.length;
      const todayPosts = posts.filter((post: any) => {
        const postDate = new Date(post.publishedAt || post.createdAt).toDateString();
        return postDate === new Date().toDateString();
      }).length;
      
      const avgFactCheckScore = posts.length > 0 
        ? Math.round(posts.reduce((sum: number, post: any) => sum + (post.factCheckScore || 0), 0) / posts.length)
        : 0;
        
      const avgSEOScore = posts.length > 0
        ? Math.round(posts.reduce((sum: number, post: any) => sum + (post.seoScore || 0), 0) / posts.length)
        : 0;
      
      setStats({
        totalPosts,
        todayPosts,
        avgFactCheckScore,
        avgSEOScore
      });
    } catch (error) {
      console.error('❌ Erro ao calcular estatísticas:', error);
    }
  };

  const handleToggleScheduler = () => {
    if (!autoScheduler) return;
    
    try {
      if (status.isRunning) {
        autoScheduler.stop();
      } else {
        autoScheduler.start();
      }
      updateStatus();
    } catch (error) {
      console.error('❌ Erro ao alternar scheduler:', error);
    }
  };

  const handleGenerateNow = async () => {
    if (!intelligentContentGenerator || isGenerating) return;
    
    setIsGenerating(true);
    try {
      console.log('🚀 Gerando post manualmente...');
      
      const keywords = [
        'energia solar residencial',
        'economia conta luz',
        'painéis solares preço',
        'energia solar vale pena'
      ];
      
      const randomKeyword = keywords[Math.floor(Math.random() * keywords.length)];
      const article = await intelligentContentGenerator.generateIntelligentArticle(randomKeyword);
      
      // Publicar via API
      const response = await fetch('/api/blog/posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: article.title,
          slug: article.slug,
          content: article.content,
          excerpt: article.excerpt,
          metaTitle: article.title,
          metaDescription: article.metaDescription,
          keywords: article.keywords || [],
          tags: article.tags || [],
          category: article.category,
          author: 'FreeEnergy',
          status: 'published',
          featured: article.featured || false,
          viral: article.viral || false,
          readTime: article.readTime || 5,
          imageUrl: 'https://images.unsplash.com/photo-1509391366360-2e959784a276?w=800&h=400&fit=crop&q=80',
          factChecked: article.factChecked || false,
          factCheckScore: article.factCheckScore || 0,
          sources: article.sources || [],
          aiGenerated: true,
          aiModel: article.aiModel || 'gemini',
          seoScore: article.seoScore || 0
        })
      });

      if (response.ok) {
        const result = await response.json();
        setLastGenerated(result.data);
        
        // Disparar evento para atualizar UI
        window.dispatchEvent(new CustomEvent('blogPostsUpdated', {
          detail: { newPost: result.data }
        }));
        
        console.log('✅ Post gerado e publicado com sucesso!');
      } else {
        throw new Error('Erro na API');
      }
      
      updateStatus();
    } catch (error) {
      console.error('❌ Erro ao gerar post:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
          <span className="ml-3 text-gray-600">Carregando sistema inteligente...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Brain className="h-8 w-8 mr-3" />
            <div>
              <h2 className="text-2xl font-bold">Blog Inteligente</h2>
              <p className="opacity-90">Sistema automatizado com IA e anti-fake news</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${status.isRunning ? 'bg-green-300 animate-pulse' : 'bg-red-300'}`}></div>
            <span className="text-sm font-medium">
              {status.isRunning ? 'Ativo' : 'Inativo'}
            </span>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="p-6 border-b">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-800">{stats.totalPosts}</div>
            <div className="text-sm text-gray-600">Posts Totais</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.todayPosts}</div>
            <div className="text-sm text-gray-600">Hoje</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center">
              <Shield className="h-5 w-5 text-blue-600 mr-1" />
              <span className="text-2xl font-bold text-blue-600">{stats.avgFactCheckScore}%</span>
            </div>
            <div className="text-sm text-gray-600">Fact-Check</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center">
              <BarChart3 className="h-5 w-5 text-purple-600 mr-1" />
              <span className="text-2xl font-bold text-purple-600">{stats.avgSEOScore}%</span>
            </div>
            <div className="text-sm text-gray-600">SEO Score</div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <motion.button
            onClick={handleToggleScheduler}
            className={`flex-1 flex items-center justify-center gap-2 px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
              status.isRunning
                ? 'bg-red-500 hover:bg-red-600 text-white'
                : 'bg-green-500 hover:bg-green-600 text-white'
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {status.isRunning ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
            {status.isRunning ? 'Pausar Sistema' : 'Iniciar Sistema'}
          </motion.button>

          <motion.button
            onClick={handleGenerateNow}
            disabled={isGenerating}
            className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-semibold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            whileHover={{ scale: isGenerating ? 1 : 1.02 }}
            whileTap={{ scale: isGenerating ? 1 : 0.98 }}
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                Gerando...
              </>
            ) : (
              <>
                <Zap className="h-5 w-5" />
                Gerar Agora
              </>
            )}
          </motion.button>
        </div>

        {/* Status Info */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <Clock className="h-5 w-5 text-gray-600 mr-2" />
              <span className="font-semibold text-gray-800">Agendamento</span>
            </div>
            <div className="text-sm text-gray-600">
              <div>Posts hoje: {status.todayPosts}/{status.maxPostsPerDay}</div>
              <div>Próximo horário: {status.nextOptimalHour}:00h</div>
              <div>Fila: {status.queueLength} posts</div>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <Settings className="h-5 w-5 text-gray-600 mr-2" />
              <span className="font-semibold text-gray-800">Keywords</span>
            </div>
            <div className="text-sm text-gray-600">
              <div>Usadas: {status.usedKeywords}/{status.totalKeywords}</div>
              <div>Disponíveis: {status.totalKeywords - status.usedKeywords}</div>
            </div>
          </div>
        </div>

        {/* Last Generated */}
        {lastGenerated && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-6 bg-green-50 border border-green-200 rounded-lg p-4"
          >
            <div className="flex items-center mb-2">
              <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              <span className="font-semibold text-green-800">Último Post Gerado</span>
            </div>
            <div className="text-sm text-green-700">
              <div className="font-medium">{lastGenerated.title}</div>
              <div className="flex items-center mt-1 space-x-4">
                <span>Fact-Check: {lastGenerated.factCheckScore}%</span>
                <span>SEO: {lastGenerated.seoScore}%</span>
                <span>Categoria: {lastGenerated.category}</span>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default IntelligentBlogControl;
