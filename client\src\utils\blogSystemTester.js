/**
 * 🧪 SISTEMA DE TESTES DO BLOG INTELIGENTE - FREEENERGY
 * Valida funcionamento completo sem erros
 */

class BlogSystemTester {
  constructor() {
    this.testResults = [];
    this.services = {};
  }

  /**
   * 🚀 EXECUTAR TODOS OS TESTES
   */
  async runAllTests() {
    console.log('🧪 Iniciando testes completos do sistema de blog...');
    
    this.testResults = [];
    
    try {
      // 1. Testar carregamento de serviços
      await this.testServiceLoading();
      
      // 2. Testar fact-checker
      await this.testFactChecker();
      
      // 3. Testar geração de conteúdo
      await this.testContentGeneration();
      
      // 4. Testar API do blog
      await this.testBlogAPI();
      
      // 5. Testar sistema de agendamento
      await this.testScheduler();
      
      // 6. Testar validações
      await this.testValidations();
      
      // 7. Gerar relatório
      this.generateTestReport();
      
      return this.testResults;
      
    } catch (error) {
      console.error('❌ Erro crítico nos testes:', error);
      this.addTestResult('CRITICAL_ERROR', false, `Erro crítico: ${error.message}`);
      return this.testResults;
    }
  }

  /**
   * 📦 TESTAR CARREGAMENTO DE SERVIÇOS
   */
  async testServiceLoading() {
    console.log('📦 Testando carregamento de serviços...');
    
    try {
      // Carregar fact-checker
      const factCheckerModule = await import('../services/factChecker.js');
      this.services.factChecker = factCheckerModule.default;
      this.addTestResult('FACT_CHECKER_LOAD', true, 'Fact-checker carregado com sucesso');
      
      // Carregar gerador de conteúdo
      const generatorModule = await import('../services/intelligentContentGenerator.js');
      this.services.generator = generatorModule.default;
      this.addTestResult('GENERATOR_LOAD', true, 'Gerador de conteúdo carregado com sucesso');
      
      // Carregar scheduler
      const schedulerModule = await import('../services/autoScheduler.js');
      this.services.scheduler = schedulerModule.default;
      this.addTestResult('SCHEDULER_LOAD', true, 'Auto-scheduler carregado com sucesso');
      
    } catch (error) {
      this.addTestResult('SERVICE_LOADING', false, `Erro ao carregar serviços: ${error.message}`);
    }
  }

  /**
   * 🛡️ TESTAR FACT-CHECKER
   */
  async testFactChecker() {
    console.log('🛡️ Testando fact-checker...');
    
    if (!this.services.factChecker) {
      this.addTestResult('FACT_CHECKER_TEST', false, 'Fact-checker não carregado');
      return;
    }

    try {
      // Teste 1: Conteúdo bom
      const goodContent = `
        <h1>Energia Solar: Economia Comprovada</h1>
        <p>A energia solar pode reduzir a conta de luz em até 85%, conforme dados da ANEEL.</p>
        <p>O retorno do investimento ocorre entre 5 e 7 anos, com painéis que duram 25 anos.</p>
      `;
      
      const goodResult = await this.services.factChecker.analyzeContent(
        goodContent,
        'Energia Solar: Economia Comprovada',
        ['aneel.gov.br', 'absolar.org.br']
      );
      
      if (goodResult.score >= 70) {
        this.addTestResult('FACT_CHECK_GOOD', true, `Conteúdo bom aprovado: ${goodResult.score}%`);
      } else {
        this.addTestResult('FACT_CHECK_GOOD', false, `Conteúdo bom rejeitado: ${goodResult.score}%`);
      }
      
      // Teste 2: Conteúdo com red flags
      const badContent = `
        <h1>ÚLTIMA CHANCE: Governo vai proibir energia solar!</h1>
        <p>Economize 150% na conta de luz com este truque simples!</p>
        <p>Ganhe dinheiro fácil sem esforço!</p>
      `;
      
      const badResult = await this.services.factChecker.analyzeContent(
        badContent,
        'ÚLTIMA CHANCE: Governo vai proibir energia solar!',
        []
      );
      
      if (badResult.score < 50) {
        this.addTestResult('FACT_CHECK_BAD', true, `Conteúdo ruim rejeitado: ${badResult.score}%`);
      } else {
        this.addTestResult('FACT_CHECK_BAD', false, `Conteúdo ruim aprovado: ${badResult.score}%`);
      }
      
    } catch (error) {
      this.addTestResult('FACT_CHECKER_TEST', false, `Erro no fact-checker: ${error.message}`);
    }
  }

  /**
   * 🤖 TESTAR GERAÇÃO DE CONTEÚDO
   */
  async testContentGeneration() {
    console.log('🤖 Testando geração de conteúdo...');
    
    if (!this.services.generator) {
      this.addTestResult('CONTENT_GENERATION', false, 'Gerador não carregado');
      return;
    }

    try {
      // Teste com keyword simples
      const article = await this.services.generator.generateIntelligentArticle(
        'energia solar residencial',
        'Energia Solar'
      );
      
      // Validar estrutura do artigo
      const validations = [
        { field: 'title', condition: article.title && article.title.length > 10 },
        { field: 'content', condition: article.content && article.content.length > 500 },
        { field: 'slug', condition: article.slug && /^[a-z0-9-]+$/.test(article.slug) },
        { field: 'factCheckScore', condition: typeof article.factCheckScore === 'number' },
        { field: 'seoScore', condition: typeof article.seoScore === 'number' }
      ];
      
      let passedValidations = 0;
      validations.forEach(validation => {
        if (validation.condition) {
          passedValidations++;
          this.addTestResult(`ARTICLE_${validation.field.toUpperCase()}`, true, `${validation.field} válido`);
        } else {
          this.addTestResult(`ARTICLE_${validation.field.toUpperCase()}`, false, `${validation.field} inválido`);
        }
      });
      
      if (passedValidations === validations.length) {
        this.addTestResult('CONTENT_GENERATION', true, 'Artigo gerado com sucesso');
      } else {
        this.addTestResult('CONTENT_GENERATION', false, `${passedValidations}/${validations.length} validações passaram`);
      }
      
    } catch (error) {
      this.addTestResult('CONTENT_GENERATION', false, `Erro na geração: ${error.message}`);
    }
  }

  /**
   * 🌐 TESTAR API DO BLOG
   */
  async testBlogAPI() {
    console.log('🌐 Testando API do blog...');
    
    try {
      // Teste 1: Listar posts
      const listResponse = await fetch('/api/blog/posts?limit=5');
      if (listResponse.ok) {
        const listData = await listResponse.json();
        this.addTestResult('API_LIST_POSTS', true, `${listData.data.length} posts listados`);
      } else {
        this.addTestResult('API_LIST_POSTS', false, `Erro ${listResponse.status}`);
      }
      
      // Teste 2: Criar post
      const testPost = {
        title: 'Teste Automatizado - Energia Solar',
        slug: 'teste-automatizado-energia-solar',
        content: '<h1>Teste</h1><p>Este é um post de teste gerado automaticamente.</p>',
        excerpt: 'Post de teste para validação do sistema.',
        category: 'Energia Solar',
        tags: ['teste', 'energia solar'],
        keywords: ['teste', 'energia solar'],
        factCheckScore: 85,
        seoScore: 80,
        aiGenerated: true
      };
      
      const createResponse = await fetch('/api/blog/posts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testPost)
      });
      
      if (createResponse.ok) {
        const createData = await createResponse.json();
        this.addTestResult('API_CREATE_POST', true, `Post criado: ID ${createData.data.id}`);
        
        // Teste 3: Buscar post criado
        const getResponse = await fetch(`/api/blog/posts/${createData.data.id}`);
        if (getResponse.ok) {
          this.addTestResult('API_GET_POST', true, 'Post recuperado com sucesso');
        } else {
          this.addTestResult('API_GET_POST', false, `Erro ${getResponse.status}`);
        }
        
        // Teste 4: Deletar post de teste
        const deleteResponse = await fetch(`/api/blog/posts/${createData.data.id}`, {
          method: 'DELETE'
        });
        
        if (deleteResponse.ok) {
          this.addTestResult('API_DELETE_POST', true, 'Post deletado com sucesso');
        } else {
          this.addTestResult('API_DELETE_POST', false, `Erro ${deleteResponse.status}`);
        }
        
      } else {
        this.addTestResult('API_CREATE_POST', false, `Erro ${createResponse.status}`);
      }
      
    } catch (error) {
      this.addTestResult('API_TESTS', false, `Erro na API: ${error.message}`);
    }
  }

  /**
   * ⏰ TESTAR SCHEDULER
   */
  async testScheduler() {
    console.log('⏰ Testando scheduler...');
    
    if (!this.services.scheduler) {
      this.addTestResult('SCHEDULER_TEST', false, 'Scheduler não carregado');
      return;
    }

    try {
      // Testar status
      const status = this.services.scheduler.getStatus();
      
      if (typeof status === 'object' && status.hasOwnProperty('isRunning')) {
        this.addTestResult('SCHEDULER_STATUS', true, 'Status obtido com sucesso');
      } else {
        this.addTestResult('SCHEDULER_STATUS', false, 'Status inválido');
      }
      
      // Testar start/stop
      if (!status.isRunning) {
        this.services.scheduler.start();
        const newStatus = this.services.scheduler.getStatus();
        
        if (newStatus.isRunning) {
          this.addTestResult('SCHEDULER_START', true, 'Scheduler iniciado com sucesso');
          
          // Parar novamente
          this.services.scheduler.stop();
          const stoppedStatus = this.services.scheduler.getStatus();
          
          if (!stoppedStatus.isRunning) {
            this.addTestResult('SCHEDULER_STOP', true, 'Scheduler parado com sucesso');
          } else {
            this.addTestResult('SCHEDULER_STOP', false, 'Falha ao parar scheduler');
          }
        } else {
          this.addTestResult('SCHEDULER_START', false, 'Falha ao iniciar scheduler');
        }
      } else {
        this.addTestResult('SCHEDULER_CONTROL', true, 'Scheduler já estava rodando');
      }
      
    } catch (error) {
      this.addTestResult('SCHEDULER_TEST', false, `Erro no scheduler: ${error.message}`);
    }
  }

  /**
   * ✅ TESTAR VALIDAÇÕES
   */
  async testValidations() {
    console.log('✅ Testando validações...');
    
    try {
      // Testar localStorage
      const testKey = 'blog_system_test';
      const testValue = { test: true, timestamp: Date.now() };
      
      localStorage.setItem(testKey, JSON.stringify(testValue));
      const retrieved = JSON.parse(localStorage.getItem(testKey) || '{}');
      
      if (retrieved.test === true) {
        this.addTestResult('LOCALSTORAGE', true, 'LocalStorage funcionando');
        localStorage.removeItem(testKey);
      } else {
        this.addTestResult('LOCALSTORAGE', false, 'LocalStorage com problemas');
      }
      
      // Testar eventos customizados
      let eventReceived = false;
      const testHandler = () => { eventReceived = true; };
      
      window.addEventListener('blogSystemTest', testHandler);
      window.dispatchEvent(new CustomEvent('blogSystemTest'));
      
      setTimeout(() => {
        if (eventReceived) {
          this.addTestResult('CUSTOM_EVENTS', true, 'Eventos customizados funcionando');
        } else {
          this.addTestResult('CUSTOM_EVENTS', false, 'Eventos customizados com problemas');
        }
        window.removeEventListener('blogSystemTest', testHandler);
      }, 100);
      
    } catch (error) {
      this.addTestResult('VALIDATIONS', false, `Erro nas validações: ${error.message}`);
    }
  }

  /**
   * 📝 ADICIONAR RESULTADO DE TESTE
   */
  addTestResult(testName, passed, message) {
    this.testResults.push({
      test: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    });
    
    const icon = passed ? '✅' : '❌';
    console.log(`${icon} ${testName}: ${message}`);
  }

  /**
   * 📊 GERAR RELATÓRIO DE TESTES
   */
  generateTestReport() {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const successRate = Math.round((passedTests / totalTests) * 100);
    
    console.log('\n📊 RELATÓRIO DE TESTES DO BLOG INTELIGENTE');
    console.log('='.repeat(50));
    console.log(`Total de testes: ${totalTests}`);
    console.log(`✅ Passou: ${passedTests}`);
    console.log(`❌ Falhou: ${failedTests}`);
    console.log(`📈 Taxa de sucesso: ${successRate}%`);
    console.log('='.repeat(50));
    
    if (failedTests > 0) {
      console.log('\n❌ TESTES QUE FALHARAM:');
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => console.log(`- ${r.test}: ${r.message}`));
    }
    
    if (successRate >= 90) {
      console.log('\n🎉 SISTEMA APROVADO! Funcionamento excelente.');
    } else if (successRate >= 70) {
      console.log('\n⚠️ SISTEMA PARCIALMENTE APROVADO. Algumas melhorias necessárias.');
    } else {
      console.log('\n🚨 SISTEMA REPROVADO. Correções críticas necessárias.');
    }
    
    return {
      totalTests,
      passedTests,
      failedTests,
      successRate,
      status: successRate >= 90 ? 'APPROVED' : successRate >= 70 ? 'PARTIAL' : 'FAILED'
    };
  }
}

// Exportar instância singleton
const blogSystemTester = new BlogSystemTester();
export default blogSystemTester;
