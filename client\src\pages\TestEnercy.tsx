import { useLocation } from 'wouter';

const TestEnercy = () => {
  const [, setLocation] = useLocation();

  const handleBackToHome = () => {
    setLocation('/');
  };

  return (
    <div className="min-h-screen bg-black text-white flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-6xl font-bold mb-8 bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent">
          ENERCY TEST PAGE
        </h1>
        <p className="text-xl mb-8">
          🎉 Roteamento funcionando perfeitamente!
        </p>
        <button
          onClick={handleBackToHome}
          className="px-8 py-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-full font-bold text-lg hover:scale-105 transition-transform"
        >
          Voltar para Home
        </button>
      </div>
    </div>
  );
};

export default TestEnercy;
