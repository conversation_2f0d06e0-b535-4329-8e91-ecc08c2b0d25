/**
 * 🚀 SISTEMA DE PUBLICAÇÃO AUTOMÁTICA - FREEENERGY
 * Publica artigos automaticamente no blog com controle de qualidade
 */

class AutoPublisher {
  constructor() {
    this.postsFile = '/blog/posts.json';
    this.viralPostsFile = '/blog/viral-posts.json';
    this.maxPostsPerFile = 20; // Limite para performance
    this.publishedPostsKey = 'freeenergy_published_posts';
  }

  /**
   * 📝 PUBLICA ARTIGO AUTOMATICAMENTE
   */
  async publishArticle(article) {
    console.log(`🚀 Publicando artigo: ${article.title}`);
    
    try {
      // Valida artigo antes de publicar
      const validatedArticle = this.validateArticle(article);
      
      // Adiciona metadados de publicação
      const publishedArticle = this.addPublishMetadata(validatedArticle);
      
      // Publica no arquivo apropriado
      const success = await this.addToPostsFile(publishedArticle);
      
      if (success) {
        // Registra publicação
        this.recordPublication(publishedArticle);
        
        // Notifica sucesso
        this.notifyPublication(publishedArticle);
        
        console.log(`✅ Artigo publicado com sucesso: ${publishedArticle.slug}`);
        return { success: true, article: publishedArticle };
      } else {
        throw new Error('Falha ao adicionar ao arquivo de posts');
      }
      
    } catch (error) {
      console.error('❌ Erro ao publicar artigo:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * ✅ VALIDA ARTIGO ANTES DA PUBLICAÇÃO
   */
  validateArticle(article) {
    console.log('🔍 Validando artigo:', {
      title: article.title?.substring(0, 50) + '...',
      titleLength: article.title?.length,
      contentLength: article.content?.length,
      hasSlug: !!article.slug,
      hasTags: Array.isArray(article.tags),
      tagsCount: article.tags?.length
    });

    const errors = [];

    // Validações mais flexíveis
    if (!article.title || article.title.trim().length < 5) {
      errors.push(`Título muito curto (${article.title?.length || 0} caracteres, mínimo 5)`);
    }

    if (!article.content || article.content.length < 100) {
      errors.push(`Conteúdo muito curto (${article.content?.length || 0} caracteres, mínimo 100)`);
    }

    if (article.metaDescription && article.metaDescription.length > 160) {
      console.log('⚠️ Meta description muito longa, será truncada');
      article.metaDescription = article.metaDescription.substring(0, 157) + '...';
    }

    if (!article.slug) {
      console.log('⚠️ Slug não fornecido, gerando automaticamente');
      article.slug = this.generateSlugFromTitle(article.title);
    }

    if (!Array.isArray(article.tags)) {
      console.log('⚠️ Tags não fornecidas, usando padrões');
      article.tags = ['energia solar', 'economia', 'sustentabilidade'];
    }

    if (errors.length > 0) {
      console.error('❌ Erros de validação:', errors);
      throw new Error(`Validação falhou: ${errors.join(', ')}`);
    }

    console.log('✅ Artigo validado com sucesso');

    // Corrige campos se necessário
    const validatedArticle = {
      ...article,
      title: this.sanitizeTitle(article.title),
      slug: this.sanitizeSlug(article.slug),
      content: this.sanitizeContent(article.content),
      tags: this.sanitizeTags(article.tags),
      metaDescription: article.metaDescription || this.generateMetaDescription(article.title)
    };

    console.log('🔧 Artigo sanitizado:', {
      title: validatedArticle.title,
      slug: validatedArticle.slug,
      metaDescription: validatedArticle.metaDescription
    });

    return validatedArticle;
  }

  /**
   * 🧹 SANITIZAÇÃO DE DADOS
   */
  sanitizeTitle(title) {
    return title.trim().substring(0, 100);
  }

  sanitizeSlug(slug) {
    return slug.toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .substring(0, 50);
  }

  sanitizeContent(content) {
    // Remove scripts maliciosos
    return content
      .replace(/<script[^>]*>.*?<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }

  sanitizeTags(tags) {
    return tags
      .filter(tag => typeof tag === 'string' && tag.trim().length > 0)
      .map(tag => tag.trim().toLowerCase())
      .slice(0, 10); // Máximo 10 tags
  }

  /**
   * 🔧 GERA SLUG A PARTIR DO TÍTULO
   */
  generateSlugFromTitle(title) {
    if (!title) return 'artigo-energia-solar';

    return title
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove acentos
      .replace(/[^a-z0-9\s-]/g, '') // Remove caracteres especiais
      .replace(/\s+/g, '-') // Substitui espaços por hífens
      .replace(/-+/g, '-') // Remove hífens duplicados
      .replace(/^-|-$/g, '') // Remove hífens do início e fim
      .substring(0, 50); // Limita tamanho
  }

  /**
   * 📝 GERA META DESCRIPTION A PARTIR DO TÍTULO
   */
  generateMetaDescription(title) {
    if (!title) return 'Artigo sobre energia solar e economia na conta de luz. Saiba mais na FreeEnergy Brasil.';

    const base = `${title}. Descubra como economizar até 95% na conta de luz com energia solar. Análise gratuita disponível.`;

    return base.length > 160 ? base.substring(0, 157) + '...' : base;
  }

  /**
   * 📊 ADICIONA METADADOS DE PUBLICAÇÃO
   */
  addPublishMetadata(article) {
    const now = new Date();
    
    return {
      ...article,
      id: `post_${Date.now()}`,
      publishedAt: now.toISOString(),
      date: now.toISOString().split('T')[0],
      author: article.author || 'FreeEnergy Brasil',
      status: 'published',
      views: 0,
      likes: 0,
      shares: 0,
      source: 'auto-generator',
      version: '1.0'
    };
  }

  /**
   * 📁 ADICIONA AO ARQUIVO DE POSTS
   */
  async addToPostsFile(article) {
    try {
      // Determina qual arquivo usar
      const targetFile = article.viral ? this.viralPostsFile : this.postsFile;
      
      // Carrega posts existentes
      const existingPosts = await this.loadExistingPosts(targetFile);
      
      // Verifica duplicatas
      if (this.isDuplicate(article, existingPosts)) {
        console.log('⚠️ Artigo duplicado detectado, pulando...');
        return false;
      }
      
      // Adiciona novo post no início
      existingPosts.unshift(article);
      
      // Limita número de posts
      if (existingPosts.length > this.maxPostsPerFile) {
        existingPosts.splice(this.maxPostsPerFile);
      }
      
      // Salva arquivo atualizado
      await this.savePostsFile(targetFile, existingPosts);
      
      return true;
    } catch (error) {
      console.error('❌ Erro ao adicionar ao arquivo:', error);
      return false;
    }
  }

  /**
   * 📖 CARREGA POSTS EXISTENTES
   */
  async loadExistingPosts(filePath) {
    try {
      const response = await fetch(filePath);
      if (response.ok) {
        const posts = await response.json();
        return Array.isArray(posts) ? posts : [];
      }
      return [];
    } catch (error) {
      console.error('❌ Erro ao carregar posts existentes:', error);
      return [];
    }
  }

  /**
   * 🔍 VERIFICA DUPLICATAS
   */
  isDuplicate(newArticle, existingPosts) {
    return existingPosts.some(post => 
      post.slug === newArticle.slug || 
      post.title === newArticle.title ||
      (post.id && post.id === newArticle.id)
    );
  }

  /**
   * 💾 SALVA ARQUIVO DE POSTS
   */
  async savePostsFile(filePath, posts) {
    // Em produção, isso seria uma chamada para API
    // Por enquanto, simula salvamento
    console.log(`💾 Simulando salvamento em ${filePath}:`, {
      totalPosts: posts.length,
      latestPost: posts[0]?.title
    });
    
    // TODO: Implementar salvamento real via API
    // await fetch('/api/save-posts', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ filePath, posts })
    // });
    
    return true;
  }

  /**
   * 📝 REGISTRA PUBLICAÇÃO
   */
  recordPublication(article) {
    try {
      const published = this.getPublishedPosts();
      const record = {
        id: article.id,
        title: article.title,
        slug: article.slug,
        publishedAt: article.publishedAt,
        viral: article.viral || false,
        category: article.category
      };
      
      published.push(record);
      
      // Mantém apenas os últimos 50 registros
      if (published.length > 50) {
        published.splice(0, published.length - 50);
      }
      
      localStorage.setItem(this.publishedPostsKey, JSON.stringify(published));
    } catch (error) {
      console.error('❌ Erro ao registrar publicação:', error);
    }
  }

  /**
   * 📋 OBTÉM POSTS PUBLICADOS
   */
  getPublishedPosts() {
    try {
      const stored = localStorage.getItem(this.publishedPostsKey);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('❌ Erro ao carregar posts publicados:', error);
      return [];
    }
  }

  /**
   * 🔔 NOTIFICA PUBLICAÇÃO
   */
  notifyPublication(article) {
    // Notificação visual
    if (typeof window !== 'undefined' && window.showNotification) {
      window.showNotification({
        title: '🚀 Artigo Publicado!',
        message: `"${article.title}" foi publicado automaticamente`,
        type: 'success',
        duration: 5000
      });
    }
    
    // Log detalhado
    console.log('🎉 PUBLICAÇÃO CONCLUÍDA:', {
      title: article.title,
      slug: article.slug,
      category: article.category,
      viral: article.viral,
      publishedAt: article.publishedAt
    });
  }

  /**
   * 📊 ESTATÍSTICAS DE PUBLICAÇÃO
   */
  getPublishingStats() {
    const published = this.getPublishedPosts();
    const today = new Date().toISOString().split('T')[0];
    
    const todayPosts = published.filter(post => 
      post.publishedAt.startsWith(today)
    );
    
    const viralPosts = published.filter(post => post.viral);
    
    const categoryStats = {};
    published.forEach(post => {
      categoryStats[post.category] = (categoryStats[post.category] || 0) + 1;
    });

    return {
      totalPublished: published.length,
      publishedToday: todayPosts.length,
      viralPosts: viralPosts.length,
      categoryStats,
      lastPublished: published[published.length - 1] || null
    };
  }

  /**
   * 🧹 LIMPEZA DE DADOS
   */
  cleanup() {
    try {
      // Remove registros muito antigos
      const published = this.getPublishedPosts();
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const filtered = published.filter(post => 
        new Date(post.publishedAt) > thirtyDaysAgo
      );
      
      localStorage.setItem(this.publishedPostsKey, JSON.stringify(filtered));
      
      console.log(`🧹 Limpeza concluída: ${published.length - filtered.length} registros removidos`);
    } catch (error) {
      console.error('❌ Erro na limpeza:', error);
    }
  }
}

export default AutoPublisher;
