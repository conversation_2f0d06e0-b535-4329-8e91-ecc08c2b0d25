/**
 * 🤖 GERADOR DE CONTEÚDO INTELIGENTE - FREEENERGY
 * Sistema avançado com validação anti-fake news e otimização SEO
 */

import factChecker from './factChecker.js';

class IntelligentContentGenerator {
  constructor() {
    this.loadAPIConfig();
    
    // URLs das APIs
    this.openaiURL = 'https://api.openai.com/v1/chat/completions';
    this.geminiURL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent';
    
    // Configurações da marca
    this.brandName = 'FreeEnergy';
    this.whatsappNumber = '5598981735618';
    this.website = 'https://free-energy-5752f.web.app';
    
    // Templates de conteúdo verificado
    this.verifiedTemplates = {
      economy: {
        min: 70,
        max: 95,
        typical: 85
      },
      payback: {
        min: 4,
        max: 8,
        typical: 6
      },
      lifespan: {
        min: 20,
        max: 25,
        typical: 25
      }
    };
  }

  /**
   * 📋 CARREGA CONFIGURAÇÃO DE API
   */
  loadAPIConfig() {
    try {
      const savedConfig = localStorage.getItem('autoblogger_api_config');
      if (savedConfig) {
        const config = JSON.parse(savedConfig);
        this.openaiKey = config.openaiKey || 'demo-key';
        this.geminiKey = config.geminiKey || 'AIzaSyDkXmv8NDUJSspRXptExlvS-yaV9J_0yBE';
        this.preferredAPI = config.preferredAPI || 'gemini';
      } else {
        this.openaiKey = 'demo-key';
        this.geminiKey = 'AIzaSyDkXmv8NDUJSspRXptExlvS-yaV9J_0yBE';
        this.preferredAPI = 'gemini';
      }
    } catch (error) {
      console.error('❌ Erro ao carregar config de API:', error);
      this.geminiKey = 'AIzaSyDkXmv8NDUJSspRXptExlvS-yaV9J_0yBE';
      this.preferredAPI = 'gemini';
    }
  }

  /**
   * 🚀 GERAR ARTIGO INTELIGENTE COM VALIDAÇÃO
   */
  async generateIntelligentArticle(keyword, category = 'Energia Solar') {
    console.log(`🤖 Gerando artigo inteligente para: ${keyword}`);
    
    try {
      // 1. Gerar conteúdo inicial
      const rawContent = await this.generateRawContent(keyword, category);
      
      // 2. Validar com fact-checker
      const factCheckResult = await factChecker.analyzeContent(
        rawContent.content,
        rawContent.title,
        this.getDefaultSources()
      );
      
      // 3. Melhorar conteúdo baseado na validação
      const improvedContent = await this.improveContentBasedOnFactCheck(
        rawContent,
        factCheckResult
      );
      
      // 4. Calcular SEO score
      const seoScore = this.calculateSEOScore(improvedContent);
      
      // 5. Preparar artigo final
      const finalArticle = {
        ...improvedContent,
        factCheckScore: factCheckResult.score,
        factChecked: factCheckResult.factChecked,
        sources: factCheckResult.sources,
        seoScore: seoScore,
        aiGenerated: true,
        aiModel: this.preferredAPI,
        trustLevel: factCheckResult.trustLevel,
        date: new Date().toISOString().split('T')[0],
        author: this.brandName,
        readTime: Math.ceil(improvedContent.content.length / 1000), // ~1000 chars = 1 min
        viral: factCheckResult.score >= 80,
        featured: factCheckResult.score >= 85
      };
      
      console.log(`✅ Artigo gerado com sucesso! Fact-check: ${factCheckResult.score}%, SEO: ${seoScore}%`);
      return finalArticle;
      
    } catch (error) {
      console.error('❌ Erro ao gerar artigo inteligente:', error);
      throw error;
    }
  }

  /**
   * 📝 GERAR CONTEÚDO BRUTO
   */
  async generateRawContent(keyword, category) {
    const prompt = this.createIntelligentPrompt(keyword, category);
    
    try {
      let response;
      if (this.preferredAPI === 'gemini') {
        response = await this.callGemini(prompt);
      } else {
        response = await this.callOpenAI(prompt);
      }
      
      return this.parseAIResponse(response);
    } catch (error) {
      console.error('❌ Erro ao gerar conteúdo bruto:', error);
      return this.generateFallbackContent(keyword, category);
    }
  }

  /**
   * 🎯 CRIAR PROMPT INTELIGENTE
   */
  createIntelligentPrompt(keyword, category) {
    return `
Você é um especialista em energia solar da ${this.brandName} Brasil.

MISSÃO: Criar artigo FACTUAL e PRECISO sobre "${keyword}".

REGRAS OBRIGATÓRIAS:
1. Use apenas dados VERIFICADOS sobre energia solar
2. Economia: entre ${this.verifiedTemplates.economy.min}% e ${this.verifiedTemplates.economy.max}%
3. Payback: entre ${this.verifiedTemplates.payback.min} e ${this.verifiedTemplates.payback.max} anos
4. Vida útil: ${this.verifiedTemplates.lifespan.typical} anos
5. NÃO use frases como "última chance", "vai acabar", "segredo"
6. SEMPRE cite fontes confiáveis

ESTRUTURA OBRIGATÓRIA:
- Título impactante mas factual
- Introdução com dados reais
- Benefícios comprovados
- Como funciona (técnico)
- Casos reais (sem exageros)
- Investimento realista
- Call-to-action profissional

RESPONDA APENAS COM JSON:
{
  "title": "Título factual sobre ${keyword}",
  "metaDescription": "Descrição SEO precisa",
  "slug": "${keyword.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')}",
  "content": "HTML completo com mínimo 1500 palavras FACTUAIS",
  "excerpt": "Resumo de 150 caracteres",
  "tags": ["${keyword}", "energia solar", "economia"],
  "category": "${category}",
  "keywords": ["palavra-chave", "relacionadas"]
}

CRÍTICO: Conteúdo deve ser 100% FACTUAL e VERIFICÁVEL!
`;
  }

  /**
   * 🤖 CHAMAR GEMINI API
   */
  async callGemini(prompt) {
    if (this.geminiKey === 'demo-key') {
      return this.getDemoResponse(prompt);
    }

    const response = await fetch(`${this.geminiURL}?key=${this.geminiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{ text: prompt }]
        }],
        generationConfig: {
          temperature: 0.7, // Menos criativo, mais factual
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 4000,
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Gemini API error: ${response.status}`);
    }

    const data = await response.json();
    return data.candidates[0].content.parts[0].text;
  }

  /**
   * 🔧 MELHORAR CONTEÚDO BASEADO NO FACT-CHECK
   */
  async improveContentBasedOnFactCheck(content, factCheckResult) {
    if (factCheckResult.score >= 80) {
      // Conteúdo já está bom
      return content;
    }

    console.log('🔧 Melhorando conteúdo baseado no fact-check...');
    
    // Aplicar correções automáticas
    let improvedContent = { ...content };
    
    // Corrigir claims de economia exagerados
    improvedContent.content = improvedContent.content.replace(
      /economize?\s+(?:até\s+)?(\d+)%/gi,
      (match, percentage) => {
        const num = parseInt(percentage);
        if (num > this.verifiedTemplates.economy.max) {
          return `economize até ${this.verifiedTemplates.economy.typical}%`;
        }
        return match;
      }
    );
    
    // Adicionar disclaimers se necessário
    if (factCheckResult.score < 70) {
      improvedContent.content += `
        <div class="disclaimer">
          <p><strong>Aviso:</strong> Os valores mencionados são estimativas baseadas em condições ideais. 
          Resultados podem variar conforme localização, consumo e condições de instalação. 
          Consulte sempre um especialista para avaliação personalizada.</p>
        </div>
      `;
    }
    
    return improvedContent;
  }

  /**
   * 📊 CALCULAR SEO SCORE
   */
  calculateSEOScore(content) {
    let score = 100;
    
    // Verificar título
    if (!content.title || content.title.length < 30) score -= 10;
    if (!content.title || content.title.length > 60) score -= 5;
    
    // Verificar meta description
    if (!content.metaDescription || content.metaDescription.length < 120) score -= 10;
    if (!content.metaDescription || content.metaDescription.length > 160) score -= 5;
    
    // Verificar conteúdo
    if (!content.content || content.content.length < 1500) score -= 15;
    
    // Verificar estrutura HTML
    if (!/<h[1-6]>/i.test(content.content)) score -= 10;
    if (!/<p>/i.test(content.content)) score -= 5;
    
    // Verificar keywords
    if (!content.keywords || content.keywords.length < 3) score -= 10;
    
    return Math.max(0, Math.min(100, score));
  }

  /**
   * 📚 OBTER FONTES PADRÃO
   */
  getDefaultSources() {
    return [
      'aneel.gov.br',
      'absolar.org.br',
      'portal.solar',
      'mme.gov.br'
    ];
  }

  /**
   * 🆘 CONTEÚDO FALLBACK
   */
  generateFallbackContent(keyword, category) {
    return {
      title: `${keyword}: Guia Completo e Confiável`,
      metaDescription: `Tudo sobre ${keyword}. Informações verificadas e atualizadas sobre energia solar no Brasil.`,
      slug: keyword.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
      content: `
        <h1>${keyword}: Informações Verificadas</h1>
        <p>Este artigo apresenta informações verificadas sobre ${keyword} no contexto da energia solar brasileira.</p>
        <h2>Benefícios Comprovados</h2>
        <p>A energia solar oferece economia real entre ${this.verifiedTemplates.economy.min}% e ${this.verifiedTemplates.economy.max}% na conta de luz.</p>
        <h2>Investimento e Retorno</h2>
        <p>O retorno do investimento ocorre tipicamente entre ${this.verifiedTemplates.payback.min} e ${this.verifiedTemplates.payback.max} anos.</p>
        <h2>Durabilidade</h2>
        <p>Os painéis solares têm vida útil de ${this.verifiedTemplates.lifespan.typical} anos com garantia.</p>
      `,
      excerpt: `Informações verificadas sobre ${keyword} e energia solar no Brasil.`,
      tags: [keyword, 'energia solar', 'economia'],
      category: category,
      keywords: [keyword, 'energia solar', 'sustentabilidade']
    };
  }

  /**
   * 📄 PARSEAR RESPOSTA DA IA
   */
  parseAIResponse(response) {
    try {
      // Extrair JSON da resposta
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      throw new Error('JSON não encontrado na resposta');
    } catch (error) {
      console.error('❌ Erro ao parsear resposta da IA:', error);
      throw error;
    }
  }

  /**
   * 🎲 RESPOSTA DEMO
   */
  getDemoResponse(prompt) {
    const keyword = prompt.match(/"([^"]+)"/)?.[1] || 'energia solar';
    return JSON.stringify({
      title: `${keyword}: Economia Comprovada na Conta de Luz`,
      metaDescription: `Descubra como ${keyword} pode reduzir sua conta de luz em até 85%. Informações verificadas e casos reais.`,
      slug: keyword.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
      content: `<h1>${keyword}: Economia Real e Comprovada</h1><p>A ${keyword} representa uma das melhores oportunidades de economia na conta de luz disponíveis no Brasil hoje.</p><h2>Benefícios Comprovados</h2><p>Estudos da ANEEL comprovam que a energia solar pode reduzir a conta de luz em até 85%, representando uma economia significativa para famílias brasileiras.</p>`,
      excerpt: `Descubra os benefícios reais da ${keyword} para sua economia doméstica.`,
      tags: [keyword, 'energia solar', 'economia'],
      category: 'Energia Solar',
      keywords: [keyword, 'energia solar', 'economia', 'sustentabilidade']
    });
  }
}

// Exportar instância singleton
const intelligentContentGenerator = new IntelligentContentGenerator();
export default intelligentContentGenerator;
