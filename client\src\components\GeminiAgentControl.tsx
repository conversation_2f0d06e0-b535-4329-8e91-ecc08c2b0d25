import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Play, 
  Pause, 
  Zap, 
  Brain, 
  TrendingUp,
  Clock,
  Target,
  Sparkles
} from 'lucide-react';

const GeminiAgentControl: React.FC = () => {
  const [agent, setAgent] = useState<any>(null);
  const [status, setStatus] = useState<any>({});
  const [isLoading, setIsLoading] = useState(true);
  const [lastPost, setLastPost] = useState<any>(null);

  useEffect(() => {
    initializeAgent();
    
    // Atualizar status a cada 10 segundos
    const interval = setInterval(updateStatus, 10000);
    return () => clearInterval(interval);
  }, []);

  const initializeAgent = async () => {
    try {
      // Carregar o Gemini Agent
      const module = await import('../services/geminiAgent.js');
      const geminiAgent = module.default;
      
      setAgent(geminiAgent);
      updateStatus();
      setIsLoading(false);
      
      console.log('🤖 Gemini Agent carregado com sucesso!');
    } catch (error) {
      console.error('❌ Erro ao carregar Gemini Agent:', error);
      setIsLoading(false);
    }
  };

  const updateStatus = () => {
    if (agent) {
      const currentStatus = agent.getStatus();
      setStatus(currentStatus);
      
      // Verificar último post
      try {
        const posts = JSON.parse(localStorage.getItem('freeenergy_generated_posts') || '[]');
        if (posts.length > 0) {
          setLastPost(posts[0]);
        }
      } catch (error) {
        console.error('Erro ao buscar posts:', error);
      }
    }
  };

  const handleStartStop = async () => {
    if (!agent) return;
    
    try {
      if (status.isRunning) {
        agent.stop();
        console.log('⏹️ Sistema parado');
      } else {
        await agent.startFullAutomation();
        console.log('🚀 Sistema iniciado');
      }
      updateStatus();
    } catch (error) {
      console.error('❌ Erro ao controlar sistema:', error);
    }
  };

  const handleGenerateNow = async () => {
    if (!agent) return;
    
    try {
      console.log('⚡ Gerando post manualmente...');
      await agent.generateAndPublishPost();
      updateStatus();
      
      // Mostrar notificação
      setTimeout(() => {
        window.location.reload();
      }, 2000);
      
    } catch (error) {
      console.error('❌ Erro ao gerar post:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-center text-white">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mr-3"></div>
          <span>Carregando Gemini Agent...</span>
        </div>
      </div>
    );
  }

  if (!agent) {
    return (
      <div className="bg-red-500 rounded-xl shadow-lg p-6 text-white">
        <h3 className="text-xl font-bold mb-2">❌ Erro ao Carregar Agent</h3>
        <p>Não foi possível carregar o Gemini Agent. Recarregue a página.</p>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl shadow-lg overflow-hidden text-white">
      {/* Header */}
      <div className="p-6 border-b border-white/20">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Brain className="h-8 w-8 mr-3" />
            <div>
              <h2 className="text-2xl font-bold">🤖 Gemini Agent</h2>
              <p className="opacity-90">Sistema 100% Automatizado • Lei de Pareto 80/20</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${status.isRunning ? 'bg-green-300 animate-pulse' : 'bg-red-300'}`}></div>
            <span className="text-sm font-medium">
              {status.isRunning ? 'ATIVO' : 'PARADO'}
            </span>
          </div>
        </div>
      </div>

      {/* Stats Pareto */}
      <div className="p-6 border-b border-white/20">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <Target className="h-5 w-5 mr-1" />
              <span className="text-2xl font-bold">{status.postsGenerated || 0}</span>
            </div>
            <div className="text-sm opacity-80">Posts Gerados</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <TrendingUp className="h-5 w-5 mr-1" />
              <span className="text-2xl font-bold">80/20</span>
            </div>
            <div className="text-sm opacity-80">Pareto</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <Sparkles className="h-5 w-5 mr-1" />
              <span className="text-2xl font-bold">MIN</span>
            </div>
            <div className="text-sm opacity-80">Esforço</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <Zap className="h-5 w-5 mr-1" />
              <span className="text-2xl font-bold">MAX</span>
            </div>
            <div className="text-sm opacity-80">Resultado</div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="p-6">
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <motion.button
            onClick={handleStartStop}
            className={`flex-1 flex items-center justify-center gap-2 px-6 py-4 rounded-lg font-bold text-lg transition-all duration-300 ${
              status.isRunning
                ? 'bg-red-500 hover:bg-red-600'
                : 'bg-green-500 hover:bg-green-600'
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {status.isRunning ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
            {status.isRunning ? 'PARAR SISTEMA' : 'INICIAR 100% AUTO'}
          </motion.button>

          <motion.button
            onClick={handleGenerateNow}
            className="flex-1 flex items-center justify-center gap-2 px-6 py-4 bg-yellow-500 hover:bg-yellow-600 rounded-lg font-bold text-lg transition-all duration-300"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Zap className="h-6 w-6" />
            GERAR AGORA
          </motion.button>
        </div>

        {/* Current Strategy */}
        <div className="bg-white/10 rounded-lg p-4 mb-4">
          <h3 className="font-bold mb-2 flex items-center">
            <Target className="h-5 w-5 mr-2" />
            Estratégia Atual (Pareto 80/20)
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="opacity-80">Keyword Foco:</span>
              <div className="font-semibold">{status.currentKeyword || 'energia solar residencial'}</div>
            </div>
            <div>
              <span className="opacity-80">Template:</span>
              <div className="font-semibold capitalize">{status.nextTemplate || 'viral'}</div>
            </div>
          </div>
        </div>

        {/* Last Generated Post */}
        {lastPost && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/10 rounded-lg p-4"
          >
            <h3 className="font-bold mb-2 flex items-center">
              <Sparkles className="h-5 w-5 mr-2" />
              Último Post Gerado
            </h3>
            <div className="text-sm">
              <div className="font-medium mb-1 line-clamp-2">{lastPost.title}</div>
              <div className="flex items-center space-x-4 opacity-80">
                <span>🤖 Gemini</span>
                <span>📊 Score: {lastPost.factCheckScore || 88}%</span>
                <span>⏱️ {lastPost.readTime || 5}min</span>
              </div>
            </div>
          </motion.div>
        )}
      </div>

      {/* Footer Info */}
      <div className="bg-black/20 p-4 text-center text-sm opacity-80">
        <div className="flex items-center justify-center space-x-4">
          <span>🤖 Powered by Gemini AI</span>
          <span>•</span>
          <span>📊 Lei de Pareto 80/20</span>
          <span>•</span>
          <span>⚡ Mínimo Esforço</span>
        </div>
      </div>
    </div>
  );
};

export default GeminiAgentControl;
