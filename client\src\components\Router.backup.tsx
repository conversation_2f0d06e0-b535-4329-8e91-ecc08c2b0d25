import { useEffect, useState } from 'react';
import Home from '@/pages/Home';
import EnercyLanding from '@/pages/EnercyLanding';
import BlogPage from '@/pages/BlogPage';
import Debug from '@/pages/Debug';
import NotFound from '@/pages/not-found';

const Router = () => {
  const [currentPath, setCurrentPath] = useState('/');

  useEffect(() => {
    // Set initial path
    setCurrentPath(window.location.pathname);

    const handlePopState = () => {
      setCurrentPath(window.location.pathname);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  console.log('🚀 Router: Current path:', currentPath);

  const renderPage = () => {
    console.log('🎯 Rendering page for path:', currentPath);

    try {
      switch (currentPath) {
        case '/':
          console.log('✅ ROUTER - Carregando Home');
          return <Home />;
        case '/enercy':
          console.log('✅ ROUTER - Carregando EnercyLanding');
          return <EnercyLanding />;
        case '/blog':
          console.log('✅ ROUTER - Carregando BlogPage');
          return <BlogPage />;
        case '/debug':
          console.log('✅ ROUTER - Carregando Debug');
          return <Debug />;
        default:
          console.log('⚠️ ROUTER - Rota não encontrada:', currentPath);
          return <NotFound />;
      }
    } catch (error) {
      console.error('Error rendering page:', error);
      return <div className="text-white bg-red-500 p-4">Error loading page: {String(error)}</div>;
    }
  };

  return (
    <div>
      {renderPage()}
    </div>
  );
};

export default Router;
