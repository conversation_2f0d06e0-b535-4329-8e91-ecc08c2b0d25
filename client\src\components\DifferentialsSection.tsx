import { motion } from 'framer-motion';
import { DollarSign, <PERSON><PERSON><PERSON><PERSON>, Clock, Award, Presentation, HeartHandshake } from 'lucide-react';

const DifferentialsSection = () => {
  const differentials = [
    {
      icon: <DollarSign className="h-8 w-8 text-[#2ECC71]" />,
      title: "Zero investimento inicial",
      description: "Muitas de nossas soluções não exigem investimento inicial, eliminando barreiras financeiras."
    },
    {
      icon: <ShieldCheck className="h-8 w-8 text-[#2ECC71]" />,
      title: "Economia garantida",
      description: "Trabalhamos com parceiros que garantem a economia prometida, sem riscos para você."
    },
    {
      icon: <Clock className="h-8 w-8 text-[#2ECC71]" />,
      title: "Rápida implementação",
      description: "Soluções que podem ser implementadas rapidamente, trazendo economia imediata."
    },
    {
      icon: <Award className="h-8 w-8 text-[#2ECC71]" />,
      title: "Parceiros certificados",
      description: "Conectamos você apenas a parceiros certificados e com excelentes avaliações."
    },
    {
      icon: <Presentation className="h-8 w-8 text-[#2ECC71]" />,
      title: "Transparência total",
      description: "Processo transparente, com demonstração clara dos benefícios e economias."
    },
    {
      icon: <HeartHandshake className="h-8 w-8 text-[#2ECC71]" />,
      title: "Suporte contínuo",
      description: "Acompanhamos todo o processo e garantimos que você esteja satisfeito com os resultados."
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl font-bold font-montserrat mb-4">
            Por que escolher a <span className="text-[#2ECC71]">Free Energy</span>?
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Como intermediadores especializados em energia, oferecemos benefícios exclusivos para nossos clientes.
          </p>
        </motion.div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {differentials.map((item, index) => (
            <motion.div 
              key={index}
              className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="flex items-start">
                <div className="bg-[#2ECC71] bg-opacity-10 p-3 rounded-lg mr-4">
                  {item.icon}
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-2">{item.title}</h3>
                  <p className="text-gray-600">{item.description}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
        
        <motion.div 
          className="mt-16 bg-gray-50 p-8 rounded-xl max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col md:flex-row items-center gap-6">
            <div className="md:w-1/2">
              <h3 className="text-2xl font-bold mb-4">Nossa <span className="text-[#2ECC71]">Missão</span></h3>
              <p className="text-gray-600 mb-4">
                Ser a principal ponte entre consumidores de energia e soluções inovadoras, 
                facilitando o acesso a alternativas mais econômicas e sustentáveis. 
              </p>
              <p className="text-gray-600">
                Acreditamos que todos merecem acesso a energia mais barata e limpa, sem barreiras financeiras ou burocráticas.
              </p>
            </div>
            <div className="md:w-1/2">
              <div className="rounded-xl overflow-hidden shadow-lg">
                <img 
                  src="https://images.unsplash.com/photo-1542601906990-b4d3fb778b09?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" 
                  alt="Equipe Free Energy" 
                  className="w-full h-64 object-cover"
                />
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default DifferentialsSection;