{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=development tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@replit/vite-plugin-shadcn-theme-json": "^0.0.4", "@tanstack/react-query": "^5.60.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "connect-pg-simple": "^10.0.0", "date-fns": "^3.6.0", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.3.0", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "input-otp": "^1.2.4", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "passport": "^0.7.0", "passport-local": "^1.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.1", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.4", "recharts": "^2.13.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.0", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.23.8", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.0.11", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.14"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}