name: 🤖 Auto Blog Generator & SEO Optimizer

on:
  schedule:
    # Executa 3x por semana (Segunda, Quarta, Sexta às 9h UTC)
    - cron: '0 9 * * 1,3,5'
  workflow_dispatch: # Permite execução manual
    inputs:
      keyword:
        description: 'Palavra-chave específica (opcional)'
        required: false
        type: string

env:
  NODE_VERSION: '18'

jobs:
  generate-blog-content:
    runs-on: ubuntu-latest
    name: 📝 Gerar Conteúdo Automatizado
    
    steps:
      - name: 🚀 Checkout Repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 🔧 Install Dependencies
        run: npm ci
        
      - name: 🤖 Generate AI Blog Content
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          CUSTOM_KEYWORD: ${{ github.event.inputs.keyword }}
        run: |
          echo "🚀 Iniciando geração de conteúdo..."
          node scripts/ai-blog-generator.js
          
      - name: 🏗️ Build Application
        run: |
          echo "🏗️ Building application..."
          npm run build
          
      - name: 🔍 SEO Audit & Optimization
        run: |
          echo "🔍 Executando auditoria SEO..."
          # Lighthouse CI para Core Web Vitals
          npx @lhci/cli@0.12.x autorun || echo "Lighthouse audit completed"
          
      - name: 📊 Update Sitemap
        run: |
          echo "📊 Atualizando sitemap..."
          # Script para atualizar sitemap.xml automaticamente
          node scripts/update-sitemap.js
          
      - name: 🚀 Deploy to Firebase
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
        run: |
          echo "🚀 Deploying to Firebase..."
          npm install -g firebase-tools
          firebase deploy --only hosting --token "$FIREBASE_TOKEN"
          
      - name: 📈 Submit to Google Search Console
        env:
          GOOGLE_INDEXING_KEY: ${{ secrets.GOOGLE_INDEXING_KEY }}
        run: |
          echo "📈 Submetendo URLs para indexação..."
          node scripts/submit-to-google.js
          
      - name: 📱 Social Media Auto-Post
        env:
          TWITTER_API_KEY: ${{ secrets.TWITTER_API_KEY }}
          TWITTER_API_SECRET: ${{ secrets.TWITTER_API_SECRET }}
          TWITTER_ACCESS_TOKEN: ${{ secrets.TWITTER_ACCESS_TOKEN }}
          TWITTER_ACCESS_SECRET: ${{ secrets.TWITTER_ACCESS_SECRET }}
        run: |
          echo "📱 Postando nas redes sociais..."
          node scripts/social-media-post.js
          
      - name: 📊 Analytics & Reporting
        run: |
          echo "📊 Gerando relatório de performance..."
          echo "✅ Blog post gerado e publicado com sucesso!"
          echo "🔗 Site: https://free-energy-5752f.web.app"
          echo "📈 Novo conteúdo indexado automaticamente"

  seo-monitoring:
    runs-on: ubuntu-latest
    name: 📈 Monitoramento SEO
    needs: generate-blog-content
    
    steps:
      - name: 🚀 Checkout Repository
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: 🔍 SEO Health Check
        run: |
          echo "🔍 Verificando saúde do SEO..."
          # Verificar Core Web Vitals
          npx lighthouse https://free-energy-5752f.web.app --output=json --output-path=./lighthouse-report.json --chrome-flags="--headless" || true
          
      - name: 📊 Performance Report
        run: |
          echo "📊 Relatório de Performance:"
          echo "✅ Site online e funcionando"
          echo "🚀 Novo conteúdo publicado"
          echo "📈 SEO otimizado automaticamente"
          
      - name: 🔔 Notification
        if: failure()
        run: |
          echo "❌ Falha na automação detectada"
          echo "🔧 Verificar logs para correção"

# CONFIGURAÇÃO NECESSÁRIA:
# 1. GitHub Secrets:
#    - OPENAI_API_KEY: Chave da OpenAI
#    - FIREBASE_TOKEN: Token do Firebase CLI
#    - GOOGLE_INDEXING_KEY: Chave da Google Indexing API
#    - TWITTER_API_*: Credenciais do Twitter (opcional)
#
# 2. Permissões:
#    - Actions: Read/Write
#    - Contents: Write
#    - Pages: Write
#
# 3. Custos Estimados:
#    - GitHub Actions: Gratuito (2000 min/mês)
#    - OpenAI API: ~$0.002 por artigo
#    - Total: ~$0.60/mês para 3 artigos/semana
