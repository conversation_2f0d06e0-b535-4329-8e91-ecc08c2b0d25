import { motion } from 'framer-motion';
import { Lightbulb, DollarSign, Leaf, BrainCircuit, ZapIcon, TreeDeciduous } from 'lucide-react';

const StorytellingSection = () => {
  const stats = [
    {
      icon: <Lightbulb className="h-10 w-10 text-[#FFC107]" />,
      title: "Até 40%",
      description: "De redução possível na conta de energia através de nossas soluções parceiras"
    },
    {
      icon: <DollarSign className="h-10 w-10 text-[#FFC107]" />,
      title: "R$ 0",
      description: "Investimento inicial para migrar para opções mais econômicas"
    },
    {
      icon: <TreeDeciduous className="h-10 w-10 text-[#FFC107]" />,
      title: "Sustentável",
      description: "Soluções parceiras com impacto ambiental positivo"
    }
  ];

  // Dados educativos para o painel de comparação
  const educationalPoints = [
    {
      title: "Processo Tradicional",
      points: [
        "Alto investimento inicial",
        "Complexidade técnica",
        "Longos prazos para retorno",
        "Riscos e manutenção por sua conta"
      ]
    },
    {
      title: "Com a Free Energy",
      points: [
        "Migração sem investimento inicial",
        "Intermediação com especialistas",
        "Economia imediata na conta",
        "Soluções parceiras confiáveis e inovadoras"
      ]
    }
  ];

  return (
    <section className="py-20 bg-white" id="educativo">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-14"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl font-bold font-montserrat mb-4">
            Entenda como <span className="text-[#2ECC71]">revolucionamos</span> a economia de energia
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Educação sobre eficiência energética, energia limpa e tecnologias sustentáveis. 
            Descubra como blockchain e inovação transformam o setor energético sem investimento inicial.
          </p>
        </motion.div>

        <div className="flex flex-col md:flex-row items-center gap-12 mb-16">
          <motion.div 
            className="md:w-1/2 order-2 md:order-1"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="bg-blue-50 p-4 rounded-lg mb-6 flex items-start gap-3">
              <BrainCircuit className="h-8 w-8 text-blue-500 flex-shrink-0 mt-1" />
              <div>
                <h3 className="font-bold text-blue-800 mb-1">Economia de Energia Simplificada</h3>
                <p className="text-gray-700 text-sm">
                  Compreender as opções de economia de energia pode ser complexo. A Free Energy simplifica esse processo,
                  educando e conectando você às melhores soluções parceiras sem complicações técnicas.
                </p>
              </div>
            </div>
            
            <h3 className="text-2xl font-bold font-montserrat mb-3">
              Nova abordagem para <span className="text-[#2ECC71]">economia energética</span>
            </h3>
            <p className="text-gray-600 mb-4">
              Na <span className="font-semibold">Free Energy</span>, somos pioneiros em tecnologia energética - 
              educadores e conectores que revolucionam o acesso à energia renovável e sustentável. 
              Conectamos você às soluções mais inovadoras: energia solar, mercado livre de energia e futuras tecnologias blockchain.
            </p>
            <p className="text-gray-600 mb-8">
              <span className="font-semibold">Nossa missão educativa:</span> Ensinar sobre as possibilidades de 
              economia de energia e facilitar a migração para opções melhores. Você economiza 
              imediatamente, sem precisar investir nada. Através de parcerias estratégicas, 
              oferecemos opções que geram economia desde o primeiro mês.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              {stats.map((stat, index) => (
                <motion.div 
                  key={index}
                  className="bg-gray-50 rounded-lg p-4 text-center shadow-sm hover:shadow-md transition-all duration-300"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <div className="flex justify-center mb-2">
                    {stat.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-[#2ECC71]">{stat.title}</h3>
                  <p className="text-sm text-gray-500">{stat.description}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>
          
          <motion.div 
            className="md:w-1/2 order-1 md:order-2"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1521618755572-156ae0cdd74d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80" 
                alt="Pessoa economizando energia" 
                className="rounded-xl shadow-lg object-cover w-full h-[500px]"
              />
              <div className="absolute -bottom-8 -right-8 bg-white p-4 rounded-lg shadow-md max-w-xs hidden md:block">
                <div className="flex items-center gap-3 mb-2">
                  <ZapIcon className="h-5 w-5 text-[#2ECC71]" />
                  <p className="font-medium">Economia sem investimento</p>
                </div>
                <p className="text-sm text-gray-600">
                  Nossos clientes migram para soluções parceiras mais econômicas sem precisar investir nada inicialmente.
                </p>
              </div>
            </div>
          </motion.div>
        </div>
        
        {/* Seção educativa com comparativo */}
        <div className="bg-gray-50 rounded-xl p-8 shadow-sm mb-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h3 className="text-2xl font-bold font-montserrat mb-6 text-center">
              Entenda a <span className="text-[#2ECC71]">diferença</span>
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {educationalPoints.map((column, colIndex) => (
                <div key={colIndex} className={`p-6 rounded-lg ${colIndex === 0 ? 'bg-gray-100' : 'bg-[#2ECC71]/10 border border-[#2ECC71]/20'}`}>
                  <h4 className={`text-xl font-bold mb-4 ${colIndex === 0 ? 'text-gray-700' : 'text-[#2ECC71]'}`}>
                    {column.title}
                  </h4>
                  <ul className="space-y-3">
                    {column.points.map((point, pointIndex) => (
                      <motion.li 
                        key={pointIndex}
                        className="flex items-center gap-2"
                        initial={{ opacity: 0, x: colIndex === 0 ? -10 : 10 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.3, delay: pointIndex * 0.1 }}
                      >
                        <div className={`h-2 w-2 rounded-full ${colIndex === 0 ? 'bg-gray-400' : 'bg-[#2ECC71]'}`}></div>
                        <span className="text-gray-700">{point}</span>
                      </motion.li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
            
            <div className="mt-8 text-center">
              <p className="text-sm text-gray-500 max-w-2xl mx-auto">
                A Free Energy não se responsabiliza diretamente pela implementação das soluções, que são fornecidas pelos parceiros. 
                Nossa função é educativa e de conexão, buscando sempre as soluções mais inovadoras e econômicas para nossos clientes.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default StorytellingSection;