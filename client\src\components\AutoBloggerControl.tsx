/**
 * 🎮 PAINEL DE CONTROLE DO AUTOBLOGGER - FREEENERGY
 * Interface para gerenciar a automação de blog
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Play, Pause, Zap, Settings, BarChart3,
  Clock, CheckCircle, AlertCircle, Trash2,
  RefreshCw, Eye, Calendar, Target, Key
} from 'lucide-react';
import useAutoBlogger from '../hooks/useAutoBlogger';
import testGeminiAPI from '../utils/testGeminiAPI';
import { simpleGeminiTest, listGeminiModels } from '../utils/simpleGeminiTest';
import testFullArticleGeneration from '../utils/testFullArticle';
import RealPublisher from '../services/realPublisher';

const AutoBloggerControl = () => {
  const {
    isActive,
    isGenerating,
    lastGeneration,
    stats,
    logs,
    config,
    startAutomation,
    stopAutomation,
    generateNow,
    updateConfig,
    clearLogs,
    resetStats,
    getStatus,
    canGenerateToday
  } = useAutoBlogger();

  const [showSettings, setShowSettings] = useState(false);
  const [showLogs, setShowLogs] = useState(false);
  const [tempConfig, setTempConfig] = useState(config);
  const [testingAPI, setTestingAPI] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const [generatedPosts, setGeneratedPosts] = useState([]);

  const realPublisher = new RealPublisher();

  const status = getStatus();

  /**
   * 📋 CARREGA POSTS GERADOS
   */
  const loadGeneratedPosts = () => {
    const posts = realPublisher.getExistingPosts();
    setGeneratedPosts(posts);
    console.log(`📋 ${posts.length} posts gerados carregados`);
  };

  /**
   * 🗑️ LIMPA POSTS GERADOS
   */
  const clearGeneratedPosts = () => {
    if (confirm('Tem certeza que deseja limpar todos os posts gerados?')) {
      realPublisher.clearAllPosts();
      setGeneratedPosts([]);
      alert('✅ Posts limpos com sucesso!');
    }
  };

  // Carrega posts gerados na inicialização
  useEffect(() => {
    loadGeneratedPosts();
  }, [lastGeneration]);

  /**
   * 🧪 TESTA GEMINI API (SIMPLES)
   */
  const handleSimpleTest = async () => {
    setTestingAPI(true);
    setTestResult(null);

    try {
      console.log('🧪 Iniciando teste simples da Gemini API...');

      // Primeiro, lista os modelos disponíveis
      const models = await listGeminiModels();
      console.log('📋 Modelos:', models);

      // Depois testa conectividade
      const result = await simpleGeminiTest();
      setTestResult(result);

      if (result.success) {
        alert(`✅ API funcionando! Modelo: ${result.modelName}\nResposta: ${result.response}`);
      } else {
        alert(`❌ Erro na API: ${result.error}`);
      }
    } catch (error) {
      setTestResult({ success: false, error: error.message });
      alert(`❌ Erro no teste: ${error.message}`);
    } finally {
      setTestingAPI(false);
    }
  };

  /**
   * 🧪 TESTA GERAÇÃO DE ARTIGO COMPLETO
   */
  const handleTestFullArticle = async () => {
    setTestingAPI(true);
    setTestResult(null);

    try {
      console.log('🧪 Iniciando teste de artigo completo...');

      const result = await testFullArticleGeneration();
      setTestResult(result);

      if (result.success) {
        const analysis = result.analysis;
        alert(`✅ Artigo completo gerado!

📊 Análise:
- Palavras: ${analysis.wordCount}
- Caracteres: ${analysis.contentLength}
- Completo: ${analysis.isComplete ? 'SIM' : 'NÃO'}
- Título: ${analysis.hasTitle ? 'SIM' : 'NÃO'}
- Conteúdo: ${analysis.hasContent ? 'SIM' : 'NÃO'}
- Tags: ${analysis.hasTags ? 'SIM' : 'NÃO'}`);
      } else {
        alert(`❌ Erro na geração: ${result.error}`);
      }
    } catch (error) {
      setTestResult({ success: false, error: error.message });
      alert(`❌ Erro no teste: ${error.message}`);
    } finally {
      setTestingAPI(false);
    }
  };

  /**
   * 🧪 TESTA GEMINI API (COMPLETO)
   */
  const handleTestAPI = async () => {
    setTestingAPI(true);
    setTestResult(null);

    try {
      const result = await testGeminiAPI();
      setTestResult(result);

      if (result.success) {
        alert('✅ API funcionando! Artigo gerado com sucesso.');
      } else {
        alert(`❌ Erro na API: ${result.error}`);
      }
    } catch (error) {
      setTestResult({ success: false, error: error.message });
      alert(`❌ Erro no teste: ${error.message}`);
    } finally {
      setTestingAPI(false);
    }
  };

  /**
   * 🎨 COMPONENTE DE STATUS
   */
  const StatusCard = ({ title, value, icon: Icon, color = "green" }) => (
    <motion.div
      className={`bg-white rounded-xl p-4 shadow-lg border-l-4 border-${color}-500`}
      whileHover={{ scale: 1.02 }}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-800">{value}</p>
        </div>
        <Icon className={`w-8 h-8 text-${color}-500`} />
      </div>
    </motion.div>
  );

  /**
   * ⚙️ MODAL DE CONFIGURAÇÕES
   */
  const SettingsModal = () => (
    <motion.div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      <motion.div
        className="bg-white rounded-2xl p-6 w-full max-w-md mx-4"
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
      >
        <h3 className="text-xl font-bold mb-4">⚙️ Configurações</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Intervalo (horas)
            </label>
            <input
              type="number"
              min="1"
              max="168"
              value={tempConfig.interval / (60 * 60 * 1000)}
              onChange={(e) => setTempConfig(prev => ({
                ...prev,
                interval: parseInt(e.target.value) * 60 * 60 * 1000
              }))}
              className="w-full px-3 py-2 border rounded-lg"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Posts máximos por dia
            </label>
            <input
              type="number"
              min="1"
              max="10"
              value={tempConfig.maxDailyPosts}
              onChange={(e) => setTempConfig(prev => ({
                ...prev,
                maxDailyPosts: parseInt(e.target.value)
              }))}
              className="w-full px-3 py-2 border rounded-lg"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              checked={tempConfig.autoStart}
              onChange={(e) => setTempConfig(prev => ({
                ...prev,
                autoStart: e.target.checked
              }))}
              className="mr-2"
            />
            <label className="text-sm">Iniciar automaticamente</label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              checked={tempConfig.enableNotifications}
              onChange={(e) => setTempConfig(prev => ({
                ...prev,
                enableNotifications: e.target.checked
              }))}
              className="mr-2"
            />
            <label className="text-sm">Ativar notificações</label>
          </div>
        </div>

        <div className="flex gap-3 mt-6">
          <button
            onClick={() => {
              updateConfig(tempConfig);
              setShowSettings(false);
            }}
            className="flex-1 bg-green-600 text-white py-2 rounded-lg hover:bg-green-700"
          >
            Salvar
          </button>
          <button
            onClick={() => {
              setTempConfig(config);
              setShowSettings(false);
            }}
            className="flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400"
          >
            Cancelar
          </button>
        </div>
      </motion.div>
    </motion.div>
  );

  /**
   * 📋 MODAL DE LOGS
   */
  const LogsModal = () => (
    <motion.div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      <motion.div
        className="bg-white rounded-2xl p-6 w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden"
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
      >
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-bold">📋 Logs do Sistema</h3>
          <div className="flex gap-2">
            <button
              onClick={clearLogs}
              className="px-3 py-1 bg-red-100 text-red-600 rounded-lg hover:bg-red-200"
            >
              <Trash2 className="w-4 h-4" />
            </button>
            <button
              onClick={() => setShowLogs(false)}
              className="px-3 py-1 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200"
            >
              ✕
            </button>
          </div>
        </div>
        
        <div className="overflow-y-auto max-h-96 space-y-2">
          {logs.map((log) => (
            <div
              key={log.id}
              className="p-3 bg-gray-50 rounded-lg text-sm"
            >
              <div className="flex justify-between items-start">
                <span className="font-mono text-xs text-gray-500">
                  {new Date(log.timestamp).toLocaleString('pt-BR')}
                </span>
              </div>
              <p className="mt-1">{log.message}</p>
            </div>
          ))}
          
          {logs.length === 0 && (
            <p className="text-center text-gray-500 py-8">
              Nenhum log disponível
            </p>
          )}
        </div>
      </motion.div>
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            🤖 AutoBlogger FreeEnergy
          </h1>
          <p className="text-gray-600">
            Sistema de geração automática de conteúdo viral
          </p>
        </motion.div>

        {/* Status Cards */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          <StatusCard
            title="Status"
            value={isActive ? "🟢 Ativo" : "🔴 Inativo"}
            icon={isActive ? CheckCircle : AlertCircle}
            color={isActive ? "green" : "red"}
          />
          <StatusCard
            title="Posts Hoje"
            value={`${status.todayGenerated}/${config.maxDailyPosts}`}
            icon={Calendar}
            color="blue"
          />
          <StatusCard
            title="Total Gerado"
            value={status.totalGenerated}
            icon={Target}
            color="purple"
          />
          <StatusCard
            title="Keywords Restantes"
            value={stats.keywords?.remainingCount || 0}
            icon={BarChart3}
            color="orange"
          />
        </div>

        {/* Controles Principais */}
        <motion.div
          className="bg-white rounded-2xl p-6 shadow-lg mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h2 className="text-xl font-bold mb-4">🎮 Controles</h2>
          
          <div className="flex flex-wrap gap-4">
            <button
              onClick={isActive ? stopAutomation : startAutomation}
              className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium ${
                isActive 
                  ? 'bg-red-600 text-white hover:bg-red-700' 
                  : 'bg-green-600 text-white hover:bg-green-700'
              }`}
            >
              {isActive ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
              {isActive ? 'Parar' : 'Iniciar'} Automação
            </button>

            <button
              onClick={generateNow}
              disabled={isGenerating || !canGenerateToday()}
              className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400"
            >
              {isGenerating ? (
                <RefreshCw className="w-5 h-5 animate-spin" />
              ) : (
                <Zap className="w-5 h-5" />
              )}
              {isGenerating ? 'Gerando...' : 'Gerar Agora'}
            </button>

            <button
              onClick={() => setShowSettings(true)}
              className="flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              <Settings className="w-5 h-5" />
              Configurações
            </button>

            <button
              onClick={() => setShowLogs(true)}
              className="flex items-center gap-2 px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
            >
              <Eye className="w-5 h-5" />
              Ver Logs
            </button>

            <a
              href="/api-config"
              className="flex items-center gap-2 px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 text-decoration-none"
            >
              <Key className="w-5 h-5" />
              Configurar APIs
            </a>

            <button
              onClick={handleSimpleTest}
              disabled={testingAPI}
              className="flex items-center gap-2 px-6 py-3 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 disabled:bg-gray-400"
            >
              {testingAPI ? (
                <RefreshCw className="w-5 h-5 animate-spin" />
              ) : (
                <CheckCircle className="w-5 h-5" />
              )}
              {testingAPI ? 'Testando...' : 'Teste Simples API'}
            </button>

            <button
              onClick={handleTestFullArticle}
              disabled={testingAPI}
              className="flex items-center gap-2 px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:bg-gray-400"
            >
              {testingAPI ? (
                <RefreshCw className="w-5 h-5 animate-spin" />
              ) : (
                <Zap className="w-5 h-5" />
              )}
              {testingAPI ? 'Testando...' : 'Teste Artigo Completo'}
            </button>

            <button
              onClick={handleTestAPI}
              disabled={testingAPI}
              className="flex items-center gap-2 px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:bg-gray-400"
            >
              {testingAPI ? (
                <RefreshCw className="w-5 h-5 animate-spin" />
              ) : (
                <CheckCircle className="w-5 h-5" />
              )}
              {testingAPI ? 'Gerando...' : 'Teste Rápido'}
            </button>

            <a
              href="/blog"
              target="_blank"
              className="flex items-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 text-decoration-none"
            >
              <Eye className="w-5 h-5" />
              Ver Blog
            </a>

            <button
              onClick={loadGeneratedPosts}
              className="flex items-center gap-2 px-6 py-3 bg-cyan-600 text-white rounded-lg hover:bg-cyan-700"
            >
              <RefreshCw className="w-5 h-5" />
              Atualizar Posts
            </button>

            <a
              href="/articles"
              className="flex items-center gap-2 px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 text-decoration-none"
            >
              <Eye className="w-5 h-5" />
              Ver Artigos Completos
            </a>
          </div>
        </motion.div>

        {/* Última Geração */}
        {lastGeneration && (
          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <h2 className="text-xl font-bold mb-4">📝 Última Geração</h2>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Data/Hora</p>
                <p className="font-medium">
                  {new Date(lastGeneration.timestamp).toLocaleString('pt-BR')}
                </p>
              </div>
              
              {lastGeneration.success ? (
                <>
                  <div>
                    <p className="text-sm text-gray-600">Keyword</p>
                    <p className="font-medium">{lastGeneration.keyword}</p>
                  </div>
                  <div className="md:col-span-2">
                    <p className="text-sm text-gray-600">Título</p>
                    <p className="font-medium">{lastGeneration.title}</p>
                  </div>
                </>
              ) : (
                <div className="md:col-span-2">
                  <p className="text-sm text-red-600">Erro</p>
                  <p className="font-medium text-red-700">{lastGeneration.error}</p>
                </div>
              )}
            </div>
          </motion.div>
        )}

        {/* Posts Gerados */}
        {generatedPosts.length > 0 && (
          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold">📝 Posts Gerados ({generatedPosts.length})</h2>
              <button
                onClick={clearGeneratedPosts}
                className="px-4 py-2 bg-red-100 text-red-600 rounded-lg hover:bg-red-200"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>

            <div className="space-y-3 max-h-64 overflow-y-auto">
              {generatedPosts.slice(0, 10).map((post, index) => (
                <div key={post.id || index} className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="font-medium text-sm">{post.title}</h3>
                      <p className="text-xs text-gray-500 mt-1">
                        {post.date} • {post.category} • {post.readTime} min
                      </p>
                    </div>
                    <a
                      href="/blog"
                      target="_blank"
                      className="text-blue-600 hover:text-blue-700 text-xs"
                    >
                      Ver →
                    </a>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Próxima Execução */}
        {isActive && status.nextRun && (
          <motion.div
            className="bg-blue-50 border border-blue-200 rounded-2xl p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="flex items-center gap-3">
              <Clock className="w-6 h-6 text-blue-600" />
              <div>
                <h3 className="font-bold text-blue-800">Próxima Execução</h3>
                <p className="text-blue-600">
                  {new Date(status.nextRun).toLocaleString('pt-BR')}
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </div>

      {/* Modals */}
      {showSettings && <SettingsModal />}
      {showLogs && <LogsModal />}
    </div>
  );
};

export default AutoBloggerControl;
