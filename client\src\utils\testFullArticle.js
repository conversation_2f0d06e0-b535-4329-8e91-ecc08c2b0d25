/**
 * 🧪 TESTE DE GERAÇÃO DE ARTIGO COMPLETO - FREEENERGY
 * Testa se a Gemini API está gerando artigos completos com conteúdo extenso
 */

const testFullArticleGeneration = async () => {
  const apiKey = 'AIzaSyDkXmv8NDUJSspRXptExlvS-yaV9J_0yBE';
  const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${apiKey}`;
  
  const keyword = 'energia solar residencial';
  
  const prompt = `
Você é um especialista em energia solar e marketing digital da FreeEnergy Brasil.

MISSÃO: Criar um artigo COMPLETO sobre "${keyword}" com CONTEÚDO EXTENSO.

OBRIGATÓRIO: O campo "content" deve ter HTML completo com pelo menos 2000 palavras!

ESTRUTURA DO CONTEÚDO HTML:
1. <h1><PERSON><PERSON><PERSON><PERSON></h1>
2. <p>Introdução impactante (200 palavras)</p>
3. <h2><PERSON>ef<PERSON><PERSON>s da ${keyword}</h2>
4. <p>Explicação detalhada (300 palavras)</p>
5. <ul>Lista de vantagens</ul>
6. <h2>Como Funciona na Prática</h2>
7. <p>Processo detalhado (300 palavras)</p>
8. <h2>Casos Reais de Sucesso</h2>
9. <blockquote>Depoimentos (200 palavras)</blockquote>
10. <h2>Investimento e Retorno</h2>
11. <p>Análise financeira (300 palavras)</p>
12. <h2>Como Começar Hoje</h2>
13. <p>Passo a passo (200 palavras)</p>
14. <div>CTA final com WhatsApp</div>

RESPONDA APENAS COM ESTE JSON (content deve ter 2000+ palavras):
{
  "title": "🔥 ${keyword}: Economia de 95% Garantida na Conta de Luz",
  "metaDescription": "EXCLUSIVO: ${keyword} reduz conta de luz em 95%. +50.000 brasileiros economizam milhares. Veja como começar hoje mesmo!",
  "slug": "${keyword.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')}",
  "content": "HTML COMPLETO COM MÍNIMO 2000 PALAVRAS AQUI",
  "tags": ["${keyword}", "energia solar", "economia", "sustentabilidade"],
  "category": "Energia Solar",
  "readTime": 10,
  "viral": true,
  "featured": true
}

CRÍTICO: O campo "content" deve ser HTML extenso com pelo menos 2000 palavras sobre ${keyword}!
`;

  try {
    console.log('🧪 Testando geração de artigo completo...');
    console.log('📝 Keyword:', keyword);
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8000, // Aumentado para permitir mais conteúdo
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH", 
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          }
        ]
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('❌ Erro na API:', response.status, errorData);
      return { success: false, error: `API Error: ${response.status}` };
    }

    const data = await response.json();
    console.log('✅ Resposta da API recebida');
    
    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      console.error('❌ Resposta inválida da API');
      return { success: false, error: 'Resposta inválida da API' };
    }
    
    const content = data.candidates[0].content.parts[0].text;
    console.log('📝 Conteúdo bruto recebido:', content.substring(0, 200) + '...');
    
    // Tenta fazer parse do JSON
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const article = JSON.parse(jsonMatch[0]);
        
        // Analisa o conteúdo
        const contentLength = article.content ? article.content.length : 0;
        const wordCount = article.content ? 
          article.content.replace(/<[^>]*>/g, '').split(/\s+/).filter(w => w.length > 0).length : 0;
        
        console.log('📊 Análise do artigo:');
        console.log('- Título:', article.title);
        console.log('- Slug:', article.slug);
        console.log('- Caracteres no conteúdo:', contentLength);
        console.log('- Palavras no conteúdo:', wordCount);
        console.log('- Tags:', article.tags);
        console.log('- Categoria:', article.category);
        
        // Verifica se o conteúdo é extenso
        const isComplete = contentLength > 1000 && wordCount > 500;
        
        return { 
          success: true, 
          article,
          analysis: {
            contentLength,
            wordCount,
            isComplete,
            hasTitle: !!article.title,
            hasContent: !!article.content,
            hasTags: Array.isArray(article.tags) && article.tags.length > 0
          }
        };
      } else {
        console.log('⚠️ JSON não encontrado na resposta');
        return { success: false, error: 'JSON não encontrado', rawContent: content };
      }
    } catch (parseError) {
      console.error('❌ Erro no parse JSON:', parseError);
      return { success: false, error: 'Erro no parse JSON', rawContent: content };
    }
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
    return { success: false, error: error.message };
  }
};

// Exporta para uso global
if (typeof window !== 'undefined') {
  window.testFullArticleGeneration = testFullArticleGeneration;
}

export default testFullArticleGeneration;
