/**
 * 🚀 SISTEMA DE GERAÇÃO AUTOMÁTICA DE CONTEÚDO - FREEENERGY
 * Gera artigos virais sobre energia solar automaticamente
 */

class ContentGenerator {
  constructor() {
    // Carrega configuração salva ou usa padrões
    this.loadAPIConfig();

    // URLs das APIs
    this.openaiURL = 'https://api.openai.com/v1/chat/completions';
    this.geminiURL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent';

    // Configurações da marca
    this.brandName = 'FreeEnergy';
    this.whatsappNumber = '5598981735618';
    this.website = 'https://free-energy-5752f.web.app';
  }

  /**
   * 📋 CARREGA CONFIGURAÇÃO DE API
   */
  loadAPIConfig() {
    try {
      const savedConfig = localStorage.getItem('autoblogger_api_config');
      if (savedConfig) {
        const config = JSON.parse(savedConfig);
        this.openaiKey = config.openaiKey || 'demo-key';
        this.geminiKey = config.geminiKey || 'AIzaSyDkXmv8NDUJSspRXptExlvS-yaV9J_0yBE';
        this.preferredAPI = config.preferredAPI || 'gemini';
      } else {
        // Configuração padrão com sua chave Gemini
        this.openaiKey = process.env.REACT_APP_OPENAI_API_KEY || 'demo-key';
        this.geminiKey = 'AIzaSyDkXmv8NDUJSspRXptExlvS-yaV9J_0yBE';
        this.preferredAPI = 'gemini';

        // Salva configuração padrão
        const defaultConfig = {
          geminiKey: this.geminiKey,
          openaiKey: this.openaiKey,
          preferredAPI: this.preferredAPI
        };
        localStorage.setItem('autoblogger_api_config', JSON.stringify(defaultConfig));
      }

      console.log(`🔧 API Config carregada: ${this.preferredAPI.toUpperCase()} como preferida`);
      console.log(`🔑 Gemini Key configurada: ${this.geminiKey.substring(0, 10)}...`);
    } catch (error) {
      console.error('❌ Erro ao carregar config de API:', error);
      // Fallback com sua chave
      this.openaiKey = 'demo-key';
      this.geminiKey = 'AIzaSyDkXmv8NDUJSspRXptExlvS-yaV9J_0yBE';
      this.preferredAPI = 'gemini';
    }
  }

  /**
   * 🎯 GERA ARTIGO COMPLETO COM SEO OTIMIZADO
   */
  async generateArticle(keyword) {
    console.log(`🔥 Gerando artigo para keyword: ${keyword} usando ${this.preferredAPI.toUpperCase()}`);

    try {
      const prompt = this.createPrompt(keyword);
      let response;

      // Tenta API preferida primeiro
      if (this.preferredAPI === 'gemini') {
        response = await this.callGemini(prompt);
      } else {
        response = await this.callOpenAI(prompt);
      }

      const article = this.parseResponse(response);
      const optimizedArticle = this.optimizeSEO(article, keyword);

      console.log(`✅ Artigo gerado com ${this.preferredAPI.toUpperCase()}: ${optimizedArticle.title}`);
      return optimizedArticle;
    } catch (error) {
      console.error(`❌ Erro com ${this.preferredAPI.toUpperCase()}:`, error);

      // Fallback para outra API
      try {
        console.log('🔄 Tentando API alternativa...');
        const prompt = this.createPrompt(keyword);
        let response;

        if (this.preferredAPI === 'gemini') {
          response = await this.callOpenAI(prompt);
        } else {
          response = await this.callGemini(prompt);
        }

        const article = this.parseResponse(response);
        const optimizedArticle = this.optimizeSEO(article, keyword);

        console.log(`✅ Artigo gerado com API alternativa: ${optimizedArticle.title}`);
        return optimizedArticle;
      } catch (fallbackError) {
        console.error('❌ Erro em ambas as APIs:', fallbackError);
        return this.generateFallbackArticle(keyword);
      }
    }
  }

  /**
   * 📝 CRIA PROMPT OTIMIZADO PARA FREEENERGY
   */
  createPrompt(keyword) {
    const basePrompt = `
Você é um especialista em energia solar e marketing digital da FreeEnergy Brasil.

MISSÃO: Criar um artigo COMPLETO sobre "${keyword}" com CONTEÚDO EXTENSO.

OBRIGATÓRIO: O campo "content" deve ter HTML completo com pelo menos 2000 palavras!

ESTRUTURA DO CONTEÚDO HTML:
1. <h1>Título Principal</h1>
2. <p>Introdução impactante (200 palavras)</p>
3. <h2>Benefícios da ${keyword}</h2>
4. <p>Explicação detalhada (300 palavras)</p>
5. <ul>Lista de vantagens</ul>
6. <h2>Como Funciona na Prática</h2>
7. <p>Processo detalhado (300 palavras)</p>
8. <h2>Casos Reais de Sucesso</h2>
9. <blockquote>Depoimentos (200 palavras)</blockquote>
10. <h2>Investimento e Retorno</h2>
11. <p>Análise financeira (300 palavras)</p>
12. <h2>Como Começar Hoje</h2>
13. <p>Passo a passo (200 palavras)</p>
14. <div>CTA final com WhatsApp</div>

EXEMPLO DE CONTEÚDO EXTENSO:
"<h1>🔥 ${keyword}: Revolução na Economia de Energia</h1>
<p>Mais de 50.000 famílias brasileiras já descobriram como ${keyword} pode transformar completamente suas contas de energia elétrica. Em um cenário onde os preços da energia não param de subir, essa tecnologia representa não apenas uma economia, mas uma verdadeira revolução financeira para quem a adota...</p>
<h2>💰 Benefícios Comprovados da ${keyword}</h2>
<p>Os números são impressionantes e comprovados por milhares de usuários em todo o Brasil. A ${keyword} oferece uma economia média de 85% a 95% na conta de luz, o que representa milhares de reais economizados anualmente...</p>"

RESPONDA APENAS COM ESTE JSON (content deve ter 2000+ palavras):
{
  "title": "🔥 ${keyword}: Economia de 95% Garantida na Conta de Luz",
  "metaDescription": "EXCLUSIVO: ${keyword} reduz conta de luz em 95%. +50.000 brasileiros economizam milhares. Veja como começar hoje mesmo!",
  "slug": "${keyword.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')}",
  "content": "HTML COMPLETO COM MÍNIMO 2000 PALAVRAS AQUI",
  "tags": ["${keyword}", "energia solar", "economia", "sustentabilidade"],
  "category": "Energia Solar",
  "readTime": 10,
  "viral": true,
  "featured": true
}

CRÍTICO: O campo "content" deve ser HTML extenso com pelo menos 2000 palavras sobre ${keyword}!
`;

    return basePrompt;
  }

  /**
   * 🤖 CHAMA GOOGLE GEMINI API
   */
  async callGemini(prompt) {
    console.log('🤖 Chamando Gemini API...');
    console.log('🔑 Usando chave:', this.geminiKey.substring(0, 10) + '...');

    if (this.geminiKey === 'demo-key' || !this.geminiKey) {
      console.log('⚠️ Usando modo demo - Gemini API key não configurada');
      return this.getDemoResponse(prompt);
    }

    try {
      const requestBody = {
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.8,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 4000,
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          }
        ]
      };

      console.log('📤 Enviando request para Gemini...');

      const response = await fetch(`${this.geminiURL}?key=${this.geminiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      console.log('📥 Resposta recebida:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Erro na resposta:', errorText);
        throw new Error(`Gemini API Error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('📊 Dados recebidos:', data);

      if (!data.candidates || data.candidates.length === 0) {
        console.error('❌ Nenhum candidato na resposta');
        throw new Error('Nenhum candidato retornado pela Gemini API');
      }

      const candidate = data.candidates[0];

      if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
        console.error('❌ Conteúdo inválido no candidato');
        throw new Error('Conteúdo inválido retornado pela Gemini API');
      }

      const content = candidate.content.parts[0].text;
      console.log('✅ Conteúdo extraído com sucesso:', content.substring(0, 100) + '...');

      return content;

    } catch (error) {
      console.error('❌ Erro na chamada Gemini:', error);
      throw error;
    }
  }

  /**
   * 🤖 CHAMA API DA OPENAI (FALLBACK)
   */
  async callOpenAI(prompt) {
    if (this.openaiKey === 'demo-key') {
      console.log('⚠️ Usando modo demo - OpenAI API key não configurada');
      return this.getDemoResponse(prompt);
    }

    const response = await fetch(this.openaiURL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.openaiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'Você é um especialista em energia solar e marketing digital da FreeEnergy Brasil.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 4000,
        temperature: 0.8
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API Error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  /**
   * 🎭 RESPOSTA DEMO PARA TESTES
   */
  getDemoResponse(prompt) {
    const keyword = prompt.match(/KEYWORD PRINCIPAL: "(.+)"/)?.[1] || 'energia solar';
    
    return JSON.stringify({
      title: `🔥 ${keyword.toUpperCase()}: Economia de 95% na Conta de Luz em 2025`,
      metaDescription: `EXCLUSIVO: Descubra como ${keyword} pode reduzir sua conta de luz em até 95%. Mais de 50.000 brasileiros já economizam. Veja como!`,
      slug: keyword.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
      content: this.generateDemoContent(keyword),
      tags: [keyword, "energia solar", "economia", "sustentabilidade", "conta de luz"],
      category: "Energia Solar",
      readTime: 8,
      viral: true,
      featured: true
    });
  }

  /**
   * 📄 GERA CONTEÚDO DEMO
   */
  generateDemoContent(keyword) {
    return `
<h1>🔥 ${keyword.toUpperCase()}: A Revolução que Está Transformando o Brasil</h1>

<p><strong>ATENÇÃO:</strong> Mais de 50.000 famílias brasileiras já descobriram como <strong>${keyword}</strong> pode reduzir a conta de luz em até 95%. E você ainda não sabe como?</p>

<h2>💰 Os Números que Vão te Chocar</h2>

<p>Uma família média em São Paulo gastava <strong>R$ 380/mês</strong> com energia elétrica. Após implementar ${keyword}, passou a pagar apenas <strong>R$ 19/mês</strong>.</p>

<ul>
<li>✅ <strong>Economia anual:</strong> R$ 4.332</li>
<li>✅ <strong>Economia em 25 anos:</strong> R$ 108.300</li>
<li>✅ <strong>ROI:</strong> 600% em 25 anos</li>
<li>✅ <strong>Payback:</strong> 4-6 anos</li>
</ul>

<h2>🚨 URGENTE: Mudanças na Legislação</h2>

<p>O governo federal está preparando mudanças que podem <strong>encarecer a energia elétrica em até 300%</strong> nos próximos 2 anos. Quem não se proteger agora pode enfrentar contas de luz astronômicas.</p>

<h2>📊 Como ${keyword} Funciona na Prática</h2>

<p>O sistema é mais simples do que você imagina:</p>

<ol>
<li><strong>Instalação:</strong> 1 dia útil</li>
<li><strong>Homologação:</strong> 30 dias</li>
<li><strong>Economia:</strong> Imediata</li>
<li><strong>Manutenção:</strong> Mínima</li>
</ol>

<h2>💬 Depoimentos Reais</h2>

<blockquote>
<p>"Minha conta era R$ 450/mês. Hoje pago R$ 32. Melhor investimento da minha vida!" - <strong>Maria Santos, Campinas/SP</strong></p>
</blockquote>

<blockquote>
<p>"Em 8 meses já economizei R$ 2.800. O sistema se pagou em menos de 3 anos." - <strong>João Silva, Ribeirão Preto/SP</strong></p>
</blockquote>

<h2>🎯 Como Começar HOJE</h2>

<p><strong>ATENÇÃO:</strong> Temos vagas limitadas para análise gratuita em sua região.</p>

<p><strong>📱 Fale conosco AGORA:</strong></p>

<p><a href="https://wa.me/5598981735618?text=Quero%20economizar%2095%25%20na%20conta%20de%20luz%20com%20${keyword.replace(/\s+/g, '%20')}" style="background: #25D366; color: white; padding: 15px 30px; text-decoration: none; border-radius: 10px; font-weight: bold; display: inline-block; margin: 10px 0;">🚀 QUERO MINHA ANÁLISE GRATUITA</a></p>

<p><strong>WhatsApp:</strong> +55 (98) 98173-5618</p>

<h2>⚠️ Não Perca Esta Oportunidade</h2>

<p>As condições atuais para ${keyword} nunca estiveram tão favoráveis:</p>

<ul>
<li>✅ <strong>Financiamento:</strong> Até 120x sem juros</li>
<li>✅ <strong>Incentivos fiscais:</strong> Até dezembro de 2024</li>
<li>✅ <strong>Tecnologia:</strong> Mais eficiente e barata</li>
<li>✅ <strong>Instalação:</strong> Rápida e sem burocracia</li>
</ul>

<p><strong>Não deixe para amanhã a economia que você pode ter hoje!</strong></p>

<p><a href="https://wa.me/5598981735618?text=URGENTE:%20Quero%20começar%20a%20economizar%20com%20${keyword.replace(/\s+/g, '%20')}" style="background: #ff4444; color: white; padding: 15px 30px; text-decoration: none; border-radius: 10px; font-weight: bold; display: inline-block; margin: 10px 0;">⚡ FALAR COM ESPECIALISTA AGORA</a></p>

<hr>

<p><em>⚠️ Este artigo contém informações baseadas em dados reais do mercado brasileiro de energia solar. A FreeEnergy é especialista em soluções sustentáveis e economia de energia.</em></p>
`;
  }

  /**
   * 🔍 PROCESSA RESPOSTA DA IA
   */
  parseResponse(response) {
    console.log('🔍 Processando resposta da IA...');
    console.log('📝 Resposta bruta:', response.substring(0, 200) + '...');

    try {
      // Tenta extrair JSON da resposta
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        console.log('✅ JSON encontrado, fazendo parse...');
        const parsed = JSON.parse(jsonMatch[0]);

        // Valida campos obrigatórios
        if (!parsed.title || parsed.title.length < 5) {
          console.log('⚠️ Título inválido no JSON, corrigindo...');
          parsed.title = this.generateFallbackTitle(response);
        }

        if (!parsed.content || parsed.content.length < 100) {
          console.log('⚠️ Conteúdo inválido no JSON, corrigindo...');
          parsed.content = this.generateFallbackContent(parsed.title || 'Energia Solar');
        }

        console.log('✅ JSON processado:', {
          title: parsed.title?.substring(0, 50) + '...',
          hasContent: !!parsed.content,
          contentLength: parsed.content?.length
        });

        return parsed;
      }

      console.log('⚠️ JSON não encontrado, criando estrutura fallback...');
      return this.createFallbackStructure(response);
    } catch (error) {
      console.error('❌ Erro ao processar resposta:', error);
      console.log('🆘 Usando estrutura fallback de emergência...');
      return this.createFallbackStructure(response);
    }
  }

  /**
   * 🛡️ ESTRUTURA FALLBACK
   */
  createFallbackStructure(content) {
    console.log('🛡️ Criando estrutura fallback...');

    const title = this.generateFallbackTitle(content);
    const slug = this.generateSlugFromTitle(title);
    const fallbackContent = this.generateFallbackContent(title);

    const fallback = {
      title,
      metaDescription: `${title.substring(0, 120)}... Saiba mais na FreeEnergy!`,
      slug,
      content: fallbackContent,
      tags: ['energia solar', 'economia', 'sustentabilidade', 'conta de luz'],
      category: 'Energia Solar',
      readTime: 6,
      viral: true,
      featured: false
    };

    console.log('✅ Estrutura fallback criada:', {
      title: fallback.title,
      slug: fallback.slug,
      contentLength: fallback.content.length
    });

    return fallback;
  }

  /**
   * 📝 GERA TÍTULO FALLBACK
   */
  generateFallbackTitle(content) {
    // Tenta extrair título da primeira linha
    const firstLine = content.split('\n')[0]?.trim();
    if (firstLine && firstLine.length > 10 && firstLine.length < 100) {
      return firstLine.replace(/[#*]/g, '').trim();
    }

    // Títulos padrão baseados em palavras-chave encontradas
    const keywords = ['solar', 'energia', 'economia', 'conta', 'luz', 'sustentável'];
    const foundKeywords = keywords.filter(keyword =>
      content.toLowerCase().includes(keyword)
    );

    if (foundKeywords.includes('solar')) {
      return '🔥 Energia Solar: Economia de até 95% na Conta de Luz';
    } else if (foundKeywords.includes('economia')) {
      return '💰 Como Economizar Milhares com Energia Sustentável';
    } else {
      return '⚡ Revolução da Energia Limpa: Transforme sua Casa';
    }
  }

  /**
   * 📄 GERA CONTEÚDO FALLBACK
   */
  generateFallbackContent(title) {
    return `
<h1>${title}</h1>

<p><strong>ATENÇÃO:</strong> Descubra como milhares de brasileiros estão economizando até 95% na conta de luz com energia solar.</p>

<h2>💰 Benefícios Comprovados</h2>

<ul>
<li>✅ <strong>Economia imediata:</strong> Reduza sua conta de luz desde o primeiro mês</li>
<li>✅ <strong>Investimento seguro:</strong> ROI garantido em 4-6 anos</li>
<li>✅ <strong>Valorização do imóvel:</strong> Aumento de até 20% no valor</li>
<li>✅ <strong>Sustentabilidade:</strong> Energia 100% limpa e renovável</li>
</ul>

<h2>📊 Números que Impressionam</h2>

<p>Uma família média que gastava <strong>R$ 350/mês</strong> com energia elétrica, após instalar energia solar, passou a pagar apenas <strong>R$ 18/mês</strong>.</p>

<p><strong>Economia anual:</strong> R$ 3.984<br>
<strong>Economia em 25 anos:</strong> R$ 99.600</p>

<h2>🚀 Como Começar</h2>

<p>O processo é mais simples do que você imagina:</p>

<ol>
<li><strong>Análise gratuita:</strong> Avaliamos seu consumo e potencial</li>
<li><strong>Projeto personalizado:</strong> Sistema dimensionado para sua necessidade</li>
<li><strong>Instalação rápida:</strong> Em apenas 1 dia útil</li>
<li><strong>Economia imediata:</strong> Resultados desde o primeiro mês</li>
</ol>

<h2>💬 Fale Conosco</h2>

<p>Não perca mais tempo pagando caro pela energia elétrica. Faça como milhares de brasileiros e comece a economizar hoje mesmo!</p>

<p><a href="https://wa.me/5598981735618?text=Quero%20economizar%20com%20energia%20solar" style="background: #25D366; color: white; padding: 15px 30px; text-decoration: none; border-radius: 10px; font-weight: bold; display: inline-block; margin: 10px 0;">📱 FALAR NO WHATSAPP</a></p>

<p><strong>WhatsApp:</strong> +55 (98) 98173-5618</p>

<hr>

<p><em>⚠️ FreeEnergy Brasil - Especialistas em energia solar e sustentabilidade.</em></p>
`;
  }

  /**
   * 🔧 GERA SLUG A PARTIR DO TÍTULO
   */
  generateSlugFromTitle(title) {
    if (!title) return 'energia-solar-economia';

    return title
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove acentos
      .replace(/[^a-z0-9\s-]/g, '') // Remove caracteres especiais
      .replace(/\s+/g, '-') // Substitui espaços por hífens
      .replace(/-+/g, '-') // Remove hífens duplicados
      .replace(/^-|-$/g, '') // Remove hífens do início e fim
      .substring(0, 50); // Limita tamanho
  }

  /**
   * 🎯 OTIMIZA SEO
   */
  optimizeSEO(article, keyword) {
    // Adiciona dados específicos da FreeEnergy
    article.author = 'FreeEnergy Brasil';
    article.date = new Date().toISOString().split('T')[0];
    article.seoKeywords = [keyword, 'energia solar', 'economia energia', 'conta de luz'];
    article.metaTitle = article.title;
    article.schema = 'Article';
    
    // Garante que o conteúdo tenha CTAs da FreeEnergy
    if (!article.content.includes('wa.me/5598981735618')) {
      article.content += this.addFreeEnergyCTA(keyword);
    }

    return article;
  }

  /**
   * 📞 ADICIONA CTA DA FREEENERGY
   */
  addFreeEnergyCTA(keyword) {
    return `
<div style="background: linear-gradient(135deg, #00ff00, #00cc00); padding: 30px; border-radius: 15px; text-align: center; margin: 30px 0; color: white;">
  <h3 style="color: white; margin-bottom: 20px;">🚀 Pronto para Economizar com ${keyword}?</h3>
  <p style="font-size: 18px; margin-bottom: 25px;">Fale com nossos especialistas AGORA e receba uma análise gratuita!</p>
  <a href="https://wa.me/5598981735618?text=Quero%20economizar%20com%20${keyword.replace(/\s+/g, '%20')}" 
     style="background: #25D366; color: white; padding: 15px 30px; text-decoration: none; border-radius: 10px; font-weight: bold; font-size: 18px; display: inline-block;">
     📱 FALAR NO WHATSAPP
  </a>
  <p style="margin-top: 15px; font-size: 14px;">+55 (98) 98173-5618 | Atendimento 24h</p>
</div>
`;
  }

  /**
   * 🆘 ARTIGO FALLBACK DE EMERGÊNCIA
   */
  generateFallbackArticle(keyword) {
    return {
      title: `🔥 ${keyword}: Economia Garantida na Conta de Luz`,
      metaDescription: `Descubra como ${keyword} pode reduzir sua conta de luz em até 95%. Análise gratuita disponível. Fale conosco!`,
      slug: keyword.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
      content: this.generateDemoContent(keyword),
      tags: [keyword, 'energia solar', 'economia'],
      category: 'Energia Solar',
      readTime: 5,
      viral: true,
      featured: false,
      author: 'FreeEnergy Brasil',
      date: new Date().toISOString().split('T')[0]
    };
  }
}

export default ContentGenerator;
